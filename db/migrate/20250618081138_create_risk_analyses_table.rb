class CreateRiskAnalysesTable < ActiveRecord::Migration[7.0]
  def change
    create_table :risk_analyses do |t|
      t.text :name, null: false
      t.integer :company_id, null: false
      t.references :registry_activity, null: false, foreign_key: true
      t.text :creation_date, null: false

      t.integer :property, null: false
      t.text :danger, null: false
      t.text :vulnerability_description, null: false
      t.text :security, null: false
      t.integer :probability, null: false
      t.integer :effect, null: false
      t.integer :risk, null: false
      t.integer :data_processing_impact_assessment, null: false

      t.timestamps
    end

    add_foreign_key :risk_analyses, :companies, column: :company_id
    add_index :risk_analyses, :company_id
  end
end
