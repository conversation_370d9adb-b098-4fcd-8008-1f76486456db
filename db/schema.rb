# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_06_18_081138) do
  create_table "absences", id: :integer, charset: "utf8", collation: "utf8_polish_ci", force: :cascade do |t|
    t.date "date"
    t.integer "user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "holiday_request_id"
    t.integer "category", default: 0, null: false
    t.boolean "visible", default: false
    t.integer "hours"
    t.index ["holiday_request_id"], name: "index_absences_on_holiday_request_id"
    t.index ["user_id"], name: "index_absences_on_user_id"
  end

  create_table "accounting_numbers", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "company_id"
    t.integer "user_id"
    t.integer "number"
    t.text "description"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "overhead", default: false
    t.boolean "locked", default: false, null: false
    t.datetime "deleted_at", precision: nil
    t.integer "bu"
    t.index ["company_id"], name: "index_accounting_numbers_on_company_id"
    t.index ["deleted_at"], name: "index_accounting_numbers_on_deleted_at"
    t.index ["user_id"], name: "index_accounting_numbers_on_user_id"
  end

  create_table "activities", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "trackable_id"
    t.string "trackable_type", limit: 100
    t.integer "owner_id"
    t.string "owner_type", limit: 100
    t.string "key"
    t.text "parameters"
    t.integer "recipient_id"
    t.string "recipient_type", limit: 100
    t.string "trackable_title"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["owner_id", "owner_type"], name: "index_activities_on_owner_id_and_owner_type"
    t.index ["recipient_id", "recipient_type"], name: "index_activities_on_recipient_id_and_recipient_type"
    t.index ["trackable_id", "trackable_type"], name: "index_activities_on_trackable_id_and_trackable_type"
    t.index ["trackable_title"], name: "index_activities_on_trackable_title"
  end

  create_table "agreement_companies", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "company_id"
    t.integer "agreement_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["agreement_id"], name: "fk_rails_5644e31a1f"
    t.index ["company_id", "agreement_id"], name: "index_agreement_companies_on_company_id_and_agreement_id", unique: true
  end

  create_table "agreement_departments", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "department_id"
    t.integer "agreement_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["agreement_id"], name: "fk_rails_e9dd982e24"
    t.index ["department_id", "agreement_id"], name: "index_agreement_departments_on_department_id_and_agreement_id", unique: true
  end

  create_table "agreements", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "name"
    t.text "content", size: :medium
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "kind", default: 0, null: false
    t.string "confirmation_button_text", default: "Accept"
    t.boolean "contract_of_employment", default: false
    t.boolean "business_to_business", default: false
    t.integer "state", default: 0
    t.datetime "published_at", precision: nil
    t.boolean "mandate_contract", default: false
    t.boolean "contract_work", default: false
  end

  create_table "allocation_versions", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "invoice_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "original", default: false
    t.index ["invoice_id"], name: "index_allocation_versions_on_invoice_id"
  end

  create_table "answer_comments", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "content"
    t.integer "evaluation_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["evaluation_id"], name: "index_answer_comments_on_evaluation_id"
  end

  create_table "api_keys", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "name"
    t.string "key_prefix"
    t.string "encrypted_key"
    t.date "expires_on"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["encrypted_key"], name: "index_api_keys_on_encrypted_key"
    t.index ["expires_on"], name: "index_api_keys_on_expires_on"
  end

  create_table "approvals", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "user_id"
    t.boolean "accepted", default: false, null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "approvable_id"
    t.string "approvable_type"
    t.datetime "accepted_at", precision: nil
    t.index ["user_id", "approvable_id", "approvable_type"], name: "index_approvals_on_user_id_and_approvable_id_and_approvable_type", unique: true
  end

  create_table "assets", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "type", null: false
    t.integer "project_id"
    t.date "requested_date"
    t.integer "requester_id"
    t.integer "activated_by_id"
    t.date "activated_date"
    t.date "expiry_date"
    t.text "notes"
    t.integer "state", default: 0, null: false
    t.integer "technology"
    t.integer "hosting"
    t.integer "environment"
    t.integer "ram_count"
    t.integer "storage"
    t.integer "machine_type"
    t.boolean "send_emails", default: false, null: false
    t.boolean "shared_storage", default: false, null: false
    t.integer "db_type"
    t.integer "db_size"
    t.boolean "db_dedicated", default: false, null: false
    t.integer "user_id"
    t.string "url"
    t.string "cms_login"
    t.string "phone"
    t.boolean "hq_aisvn_services", default: false, null: false
    t.boolean "dev_server", default: false, null: false
    t.boolean "monitoring", default: false, null: false
    t.string "domain"
    t.boolean "wildcard", default: false, null: false
    t.string "g_account"
    t.boolean "g_analytics", default: false, null: false
    t.boolean "re_captcha", default: false, null: false
    t.boolean "g_tag_manager", default: false, null: false
    t.boolean "ad_words", default: false, null: false
    t.boolean "g_maps", default: false, null: false
    t.boolean "g_search_console", default: false, null: false
    t.string "name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "backup"
    t.integer "ssl_type"
    t.boolean "prod_server", default: false, null: false
    t.boolean "other", default: false, null: false
    t.string "receivers_addresses"
    t.boolean "other_access", default: false, null: false
    t.boolean "internally_managed", default: false, null: false
    t.boolean "internally_purchased", default: false, null: false
    t.boolean "elastic_search", default: false, null: false
    t.string "host"
    t.boolean "g_mail_account", default: false
    t.boolean "g_optimize", default: false
    t.datetime "ssl_cert_expiry_date", precision: nil
    t.integer "ssl_cert_notification_days"
    t.string "passed_to_execution_by_id"
    t.datetime "passed_to_execution_date", precision: nil
    t.string "decommission_comment"
    t.integer "passed_to_decommission_by_id"
    t.datetime "passed_to_decommission_date", precision: nil
    t.bigint "kubernetes_cluster_id"
    t.decimal "vcpu_count", precision: 5, scale: 2
    t.text "repos_to_send_key"
    t.index ["activated_by_id"], name: "index_assets_on_activated_by_id"
    t.index ["kubernetes_cluster_id"], name: "index_assets_on_kubernetes_cluster_id"
    t.index ["name", "kubernetes_cluster_id"], name: "index_assets_on_name_and_kubernetes_cluster_id", unique: true
    t.index ["project_id"], name: "index_assets_on_project_id"
    t.index ["requester_id"], name: "index_assets_on_requester_id"
    t.index ["user_id"], name: "index_assets_on_user_id"
  end

  create_table "attachment_wysiwygs", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.text "file_data", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "attachments", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "attachable_id"
    t.text "file_data", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "attachable_type"
    t.index ["attachable_id"], name: "index_attachments_on_attachable_id"
    t.index ["attachable_type"], name: "index_attachments_on_attachable_type"
  end

  create_table "bank_accounts", id: :integer, charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.string "name"
    t.string "account_number"
    t.integer "company_id"
    t.boolean "default", default: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "currency", default: 0
    t.string "bank_name"
    t.index ["company_id"], name: "index_bank_accounts_on_company_id"
  end

  create_table "bios", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.text "desc_pl"
    t.text "desc_en"
    t.text "agreement_notes"
    t.date "publication_agreement"
    t.text "image_data"
    t.text "text"
    t.integer "user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id"], name: "index_bios_on_user_id"
  end

  create_table "booking_resources", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "name"
    t.string "identifier"
    t.string "email"
    t.integer "multiple_bookings"
    t.integer "kind"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["identifier"], name: "index_booking_resources_on_identifier", unique: true
  end

  create_table "calendar_months", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "year", null: false
    t.integer "month", null: false
    t.text "days"
    t.integer "total_hours", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id", "year", "month"], name: "index_calendar_months_on_user_id_and_year_and_month", unique: true
    t.index ["user_id"], name: "index_calendar_months_on_user_id"
  end

  create_table "cards", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "company_id"
    t.string "code"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.date "expires_on"
    t.boolean "active", default: true
    t.index ["company_id"], name: "index_cards_on_company_id"
    t.index ["user_id"], name: "index_cards_on_user_id"
  end

  create_table "client_addresses", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "client_id"
    t.string "name"
    t.string "street"
    t.string "additional_address"
    t.string "city"
    t.string "postcode"
    t.string "country"
    t.string "vat_number"
    t.string "street_number"
    t.string "apartment"
    t.string "post"
    t.string "voivodeship"
    t.string "district"
    t.string "community"
    t.string "identifier"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "invoice_sending_method", default: 0
    t.string "invoice_sending_email"
    t.string "invoice_sending_email_receiver"
    t.boolean "download_from_gus", default: false
    t.boolean "vat_payer", default: false
    t.index ["client_id"], name: "index_client_addresses_on_client_id"
  end

  create_table "clients", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "name"
    t.string "street"
    t.string "additional_address"
    t.string "city"
    t.string "postcode"
    t.string "country"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "vat_number"
    t.string "street_number"
    t.string "apartment"
    t.string "post"
    t.string "voivodeship"
    t.string "district"
    t.string "community"
    t.integer "invoice_sending_method"
    t.string "invoice_sending_email"
    t.string "invoice_sending_email_receiver"
    t.boolean "download_from_gus", default: false
    t.boolean "vat_payer", default: false
    t.integer "state", default: 0
  end

  create_table "companies", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "name"
    t.string "nip"
    t.string "address1"
    t.string "address2"
    t.string "city"
    t.string "zipcode"
    t.string "phone"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "domain"
    t.boolean "accounting", default: false
    t.integer "holiday_issue_id"
    t.integer "holiday_project_id"
    t.boolean "show_onboarding", default: false
    t.datetime "deleted_at", precision: nil
    t.text "redmine_holiday_issues_ids"
    t.boolean "click_up_synchronization", default: false
    t.boolean "redmine_synchronization", default: true
    t.text "click_up_workspace_id"
    t.index ["deleted_at"], name: "index_companies_on_deleted_at"
    t.index ["domain"], name: "index_companies_on_domain", unique: true
    t.index ["name"], name: "index_companies_on_name", unique: true
  end

  create_table "completed_payments", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "payment_id", null: false
    t.integer "project_id", null: false
    t.integer "mpk_number_id", null: false
    t.integer "amount", null: false
    t.date "issued_on", null: false
    t.integer "currency", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "invoice_id", null: false
    t.index ["invoice_id"], name: "index_completed_payments_on_invoice_id"
    t.index ["mpk_number_id"], name: "index_completed_payments_on_mpk_number_id"
    t.index ["payment_id"], name: "index_completed_payments_on_payment_id"
    t.index ["project_id"], name: "index_completed_payments_on_project_id"
  end

  create_table "contractors", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "user_id"
    t.string "name"
    t.string "street"
    t.string "street_number"
    t.string "apartment"
    t.string "additional_address"
    t.string "city"
    t.string "postcode"
    t.string "country"
    t.string "vat_number"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "post"
    t.string "voivodeship"
    t.string "district"
    t.string "community"
    t.string "account_number"
    t.boolean "download_from_gus", default: false
    t.boolean "vat_payer", default: false
    t.integer "state"
    t.integer "created_by_id"
    t.index ["created_by_id"], name: "index_contractors_on_created_by_id"
    t.index ["user_id"], name: "index_contractors_on_user_id", unique: true
    t.index ["vat_number"], name: "index_contractors_on_vat_number", unique: true
  end

  create_table "cost_account_numbers", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.string "number"
    t.string "description"
    t.boolean "active", default: true
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "default", default: false
    t.integer "kind", default: 0
    t.index ["kind"], name: "index_cost_account_numbers_on_kind"
    t.index ["number"], name: "index_cost_account_numbers_on_number", unique: true
  end

  create_table "cost_allocation_template_positions", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.bigint "cost_allocation_template_id", null: false
    t.integer "department_id"
    t.integer "accounting_number_id"
    t.decimal "share_amount", precision: 8, scale: 5, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["accounting_number_id"], name: "index_cost_allocation_template_positions_on_accounting_number_id"
    t.index ["cost_allocation_template_id"], name: "index_cost_allocation_template_positions_on_template_id"
    t.index ["department_id"], name: "index_cost_allocation_template_positions_on_department_id"
  end

  create_table "cost_allocation_templates", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "user_id", null: false
    t.string "name", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_cost_allocation_templates_on_user_id"
  end

  create_table "cost_invoice_acceptances", id: :integer, charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "cost_invoice_id"
    t.integer "user_id"
    t.datetime "accepted_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "department_id"
    t.integer "kind"
    t.datetime "deleted_at", precision: nil
    t.boolean "replacement", default: false
    t.index ["cost_invoice_id"], name: "index_cost_invoice_acceptances_on_cost_invoice_id"
    t.index ["deleted_at"], name: "index_cost_invoice_acceptances_on_deleted_at"
    t.index ["department_id"], name: "index_cost_invoice_acceptances_on_department_id"
    t.index ["user_id"], name: "index_cost_invoice_acceptances_on_user_id"
  end

  create_table "cost_invoice_positions", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "cost_invoice_id"
    t.string "name"
    t.decimal "amount", precision: 12, scale: 2
    t.decimal "unit_price", precision: 12, scale: 2
    t.integer "tax_rate"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "jpk_gtu"
    t.string "jpk_transaction_code"
    t.decimal "net_value", precision: 12, scale: 2
    t.index ["cost_invoice_id"], name: "index_cost_invoice_positions_on_cost_invoice_id"
  end

  create_table "cost_invoices", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "title"
    t.date "sell_date"
    t.date "due_date"
    t.date "invoice_date"
    t.text "description"
    t.integer "company_id"
    t.integer "contractor_id"
    t.integer "state"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "auto_cost_projects"
    t.string "number"
    t.integer "user_id"
    t.datetime "accepted_at", precision: nil
    t.text "document_data"
    t.string "type"
    t.integer "payment_method"
    t.string "account_number"
    t.integer "card_holder_id"
    t.boolean "paid"
    t.date "paid_on"
    t.integer "flow"
    t.datetime "pending_department_at", precision: nil
    t.datetime "pending_department_uber_at", precision: nil
    t.datetime "pending_controller_at", precision: nil
    t.datetime "notified_at", precision: nil
    t.integer "currency", default: 0
    t.string "deletion_reason"
    t.bigint "card_id"
    t.integer "kind", default: 0
    t.string "original_document_number"
    t.boolean "sent", default: false, null: false
    t.decimal "hours_worked", precision: 6, scale: 2
    t.decimal "hours_reported", precision: 6, scale: 2
    t.string "issuer_comment", limit: 360
    t.integer "cash_payer_id"
    t.decimal "before_invoice_date_currency_rate", precision: 30, scale: 4
    t.text "removed_contractor_data"
    t.decimal "gross_amount", precision: 20, scale: 2
    t.index ["card_holder_id"], name: "index_cost_invoices_on_card_holder_id"
    t.index ["card_id"], name: "index_cost_invoices_on_card_id"
    t.index ["cash_payer_id"], name: "index_cost_invoices_on_cash_payer_id"
    t.index ["company_id"], name: "index_cost_invoices_on_company_id"
    t.index ["contractor_id"], name: "index_cost_invoices_on_contractor_id"
    t.index ["user_id"], name: "index_cost_invoices_on_user_id"
  end

  create_table "cost_projects", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "cost_invoice_id"
    t.integer "project_id"
    t.decimal "amount", precision: 12, scale: 2
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "department_id"
    t.bigint "cost_account_number_id"
    t.bigint "accounting_number_id"
    t.index ["accounting_number_id"], name: "index_cost_projects_on_accounting_number_id"
    t.index ["cost_account_number_id"], name: "index_cost_projects_on_cost_account_number_id"
    t.index ["cost_invoice_id"], name: "index_cost_projects_on_cost_invoice_id"
    t.index ["department_id"], name: "index_cost_projects_on_department_id"
    t.index ["project_id"], name: "index_cost_projects_on_project_id"
  end

  create_table "default_agreement_contents", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "company_id"
    t.boolean "business_to_business"
    t.text "content"
  end

  create_table "departments", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "company_id"
    t.string "name"
    t.boolean "locked", default: false, null: false
    t.integer "chief_id"
    t.integer "substitute_chief_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "uber_chief_id"
    t.boolean "time_reports_not_required", default: false
    t.integer "mpk_number_id"
    t.boolean "notify_time_collectively", default: false
    t.boolean "pm", default: false
    t.integer "supervisor_id"
    t.boolean "board_member", default: false
    t.integer "role_id"
    t.index ["mpk_number_id"], name: "index_departments_on_mpk_number_id"
    t.index ["role_id"], name: "index_departments_on_role_id"
  end

  create_table "docs_file_comments", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.string "content"
    t.bigint "docs_file_id", null: false
    t.integer "created_by_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_docs_file_comments_on_created_by_id"
    t.index ["docs_file_id"], name: "index_docs_file_comments_on_docs_file_id"
  end

  create_table "docs_files", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "project_id", null: false
    t.string "file_name"
    t.integer "category"
    t.integer "created_by_id"
    t.integer "docs_file_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_docs_files_on_created_by_id"
    t.index ["docs_file_id"], name: "index_docs_files_on_docs_file_id", unique: true
    t.index ["project_id", "file_name", "category"], name: "index_docs_files_on_project_id_and_file_name_and_category", unique: true
    t.index ["project_id"], name: "index_docs_files_on_project_id"
  end

  create_table "email_aliases", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "email"
    t.integer "user_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["email"], name: "index_email_aliases_on_email", unique: true
    t.index ["user_id"], name: "index_email_aliases_on_user_id"
  end

  create_table "evaluation_additional_users", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "user_id"
    t.integer "evaluation_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["evaluation_id"], name: "index_evaluation_additional_users_on_evaluation_id"
    t.index ["user_id"], name: "index_evaluation_additional_users_on_user_id"
  end

  create_table "evaluation_answers", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "respondent_id", null: false
    t.integer "evaluation_iteration_id", null: false
    t.integer "communicativeness"
    t.integer "diligence"
    t.integer "promptness"
    t.integer "commitment"
    t.integer "independence"
    t.text "strengths"
    t.text "weaknesses"
    t.text "comments"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["created_at"], name: "index_evaluation_answers_on_created_at"
    t.index ["evaluation_iteration_id"], name: "index_evaluation_answers_on_evaluation_iteration_id"
    t.index ["respondent_id", "evaluation_iteration_id"], name: "index_unique_en_as_on_rt_id_en_and_in_id", unique: true
    t.index ["updated_at"], name: "index_evaluation_answers_on_updated_at"
  end

  create_table "evaluation_iteration_assignments", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "evaluation_iteration_id", null: false
    t.string "token"
    t.index ["evaluation_iteration_id"], name: "index_en_in_as_on_en_in_id"
    t.index ["token"], name: "index_evaluation_iteration_assignments_on_token"
    t.index ["user_id", "evaluation_iteration_id"], name: "index_unique_en_in_as_on_ur_id_and_en_in_id", unique: true
  end

  create_table "evaluation_iterations", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "evaluation_id", null: false
    t.date "deadline_on", null: false
    t.integer "number", null: false
    t.boolean "completed", default: false, null: false
    t.text "summary"
    t.decimal "communicativeness_avg", precision: 15, scale: 14
    t.decimal "diligence_avg", precision: 15, scale: 14
    t.decimal "promptness_avg", precision: 15, scale: 14
    t.decimal "commitment_avg", precision: 15, scale: 14
    t.decimal "independence_avg", precision: 15, scale: 14
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["created_at"], name: "index_evaluation_iterations_on_created_at"
    t.index ["deadline_on"], name: "index_evaluation_iterations_on_deadline_on"
    t.index ["evaluation_id"], name: "index_evaluation_iterations_on_evaluation_id"
    t.index ["number", "evaluation_id"], name: "index_evaluation_iterations_on_number_and_evaluation_id", unique: true
    t.index ["updated_at"], name: "index_evaluation_iterations_on_updated_at"
  end

  create_table "evaluations", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "user_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.date "starts_on", null: false
    t.date "ends_on"
    t.string "name"
    t.integer "state", default: 0
    t.string "comment"
    t.integer "main_survey_id"
    t.integer "additional_survey_id"
    t.integer "created_by_id"
    t.index ["additional_survey_id"], name: "index_evaluations_on_additional_survey_id"
    t.index ["created_at"], name: "index_evaluations_on_created_at"
    t.index ["created_by_id"], name: "index_evaluations_on_created_by_id"
    t.index ["main_survey_id"], name: "index_evaluations_on_main_survey_id"
    t.index ["updated_at"], name: "index_evaluations_on_updated_at"
    t.index ["user_id"], name: "fk_rails_ef42eba623"
  end

  create_table "external_costs", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "description"
    t.integer "amount"
    t.integer "payment_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "contractor_id"
    t.bigint "mpk_number_id"
    t.bigint "project_id"
    t.date "cost_date"
    t.integer "currency", default: 0
    t.text "comment"
    t.integer "created_by_id"
    t.index ["contractor_id"], name: "index_external_costs_on_contractor_id"
    t.index ["created_by_id"], name: "index_external_costs_on_created_by_id"
    t.index ["mpk_number_id"], name: "index_external_costs_on_mpk_number_id"
    t.index ["payment_id"], name: "index_external_costs_on_payment_id"
    t.index ["project_id"], name: "index_external_costs_on_project_id"
  end

  create_table "global_roles", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.text "activities"
    t.boolean "global_admin", default: false
    t.boolean "pm", default: false
    t.boolean "holiday_requests_notifications", default: false
    t.boolean "notify_dms_controller_acceptances"
    t.boolean "remote_work_periods_notifications", default: false
    t.boolean "processed_invoices_notification", default: false
  end

  create_table "group_memberships", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "user_id"
    t.integer "group_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["group_id"], name: "index_group_memberships_on_group_id"
    t.index ["user_id", "group_id"], name: "index_group_memberships_on_user_id_and_group_id", unique: true
    t.index ["user_id"], name: "index_group_memberships_on_user_id"
  end

  create_table "groups", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "redmine_id"
    t.boolean "edit_locked", default: false
    t.bigint "gid_number"
    t.boolean "chat", default: false
    t.string "cn"
    t.boolean "admin_gid", default: false
    t.index ["cn"], name: "index_groups_on_cn", unique: true
    t.index ["gid_number"], name: "index_groups_on_gid_number", unique: true
    t.index ["name"], name: "index_groups_on_name", unique: true
    t.index ["redmine_id"], name: "index_groups_on_redmine_id", unique: true
  end

  create_table "holiday_requests", id: :integer, charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.integer "applicant_id", null: false
    t.integer "examiner_id"
    t.integer "category", default: 0, null: false
    t.boolean "visible", default: false
    t.text "applicant_comment", size: :medium
    t.text "examiner_comment", size: :medium
    t.date "starts_on", null: false
    t.date "ends_on", null: false
    t.datetime "accepted_at", precision: nil
    t.datetime "rejected_at", precision: nil
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "absences_count", default: 0, null: false
    t.integer "created_by_user_id"
    t.integer "updated_by_user_id"
    t.datetime "modified_by_user_at", precision: nil
    t.boolean "confirmed", default: false
    t.text "examiner_comment_history", size: :medium
    t.text "file_data", size: :medium
    t.integer "subcategory"
    t.integer "hours"
    t.string "outlook_calendar_event_id"
    t.boolean "non_working_day_request", default: false, null: false
    t.index ["created_at"], name: "index_holiday_requests_on_created_at"
    t.index ["ends_on"], name: "index_holiday_requests_on_ends_on"
    t.index ["starts_on"], name: "index_holiday_requests_on_starts_on"
    t.index ["updated_at"], name: "index_holiday_requests_on_updated_at"
  end

  create_table "inventory_items", id: :integer, charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "user_id"
    t.integer "requester_id"
    t.string "name"
    t.text "description"
    t.date "handed_on"
    t.string "inventory_no"
    t.integer "company_id"
    t.text "comment"
    t.integer "location"
    t.integer "state"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["company_id"], name: "index_inventory_items_on_company_id"
    t.index ["inventory_no"], name: "index_inventory_items_on_inventory_no"
    t.index ["requester_id"], name: "index_inventory_items_on_requester_id"
    t.index ["state"], name: "index_inventory_items_on_state"
    t.index ["user_id"], name: "index_inventory_items_on_user_id"
  end

  create_table "invoice_documents", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.text "document_data"
    t.integer "invoice_id", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["invoice_id"], name: "index_invoice_documents_on_invoice_id", unique: true
  end

  create_table "invoice_positions", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "invoice_id"
    t.string "name"
    t.decimal "amount", precision: 8, scale: 3
    t.decimal "unit_price", precision: 9, scale: 2
    t.integer "tax_rate"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "jpk_gtu"
    t.string "jpk_transaction_code"
    t.index ["invoice_id"], name: "index_invoice_positions_on_invoice_id"
  end

  create_table "invoice_required_attachments", id: :integer, charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "invoice_id"
    t.integer "attachment_id"
    t.index ["attachment_id"], name: "fk_rails_dd6c41b891"
    t.index ["invoice_id"], name: "fk_rails_1b48bfeef0"
  end

  create_table "invoices", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.text "title"
    t.date "sell_date"
    t.date "due_date"
    t.date "invoice_date"
    t.string "description"
    t.integer "revenue_account_id"
    t.integer "payment_id"
    t.string "receiver_name"
    t.integer "state", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "street"
    t.string "additional_address"
    t.string "city"
    t.string "postcode"
    t.string "country"
    t.integer "total_amount"
    t.string "correction_reason"
    t.integer "user_id"
    t.string "client_name"
    t.string "file_name"
    t.string "number"
    t.string "street_number"
    t.string "apartment"
    t.string "post"
    t.string "voivodeship"
    t.string "district"
    t.string "community"
    t.boolean "no_attachment", default: false
    t.integer "client_address_id"
    t.string "vat_number"
    t.datetime "issued_at", precision: nil
    t.integer "kind", default: 0
    t.integer "associated_advance_invoice_id"
    t.integer "total_order_amount"
    t.integer "bank_account_id"
    t.string "bank_account_number"
    t.boolean "attachments_required"
    t.datetime "accepted_at", precision: nil
    t.index ["bank_account_id"], name: "index_invoices_on_bank_account_id"
    t.index ["client_address_id"], name: "index_invoices_on_client_address_id"
    t.index ["payment_id"], name: "index_invoices_on_payment_id"
    t.index ["revenue_account_id"], name: "index_invoices_on_revenue_account_id"
    t.index ["state"], name: "index_invoices_on_state"
    t.index ["user_id"], name: "index_invoices_on_user_id"
  end

  create_table "issues", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "redmine_id"
    t.integer "project_id", null: false
    t.string "subject"
    t.decimal "estimated_hours", precision: 24, scale: 14
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "tracker"
    t.string "status"
    t.boolean "closed", default: false, null: false
    t.string "click_up_id"
    t.index ["click_up_id"], name: "index_issues_on_click_up_id", unique: true
    t.index ["project_id"], name: "index_issues_on_project_id"
    t.index ["redmine_id"], name: "index_issues_on_redmine_id", unique: true
  end

  create_table "kubernetes_clusters", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.string "name"
    t.boolean "production", default: false
    t.text "comment"
    t.string "key_url"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "ldap_entries", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "entryable_id"
    t.string "entryable_type"
    t.string "dn"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.text "ldap_attributes"
    t.index ["dn"], name: "index_ldap_entries_on_dn"
    t.index ["entryable_type", "entryable_id"], name: "index_ldap_entries_on_entryable_type_and_entryable_id"
  end

  create_table "links", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "name"
    t.integer "bio_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["bio_id"], name: "index_links_on_bio_id"
  end

  create_table "membership_roles", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "membership_id"
    t.integer "role_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "inherited_from_id"
    t.boolean "auto_created", default: false
    t.index ["inherited_from_id"], name: "index_membership_roles_on_inherited_from_id"
    t.index ["membership_id"], name: "index_membership_roles_on_membership_id"
    t.index ["role_id", "membership_id", "inherited_from_id"], name: "role_membership_inherited_index", unique: true
    t.index ["role_id"], name: "index_membership_roles_on_role_id"
  end

  create_table "memberships", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "member_id"
    t.string "member_type"
    t.integer "project_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["member_id"], name: "index_memberships_on_member_id"
    t.index ["member_type", "member_id"], name: "index_memberships_on_member_type_and_member_id"
    t.index ["project_id", "member_id", "member_type"], name: "index_memberships_on_project_id_and_member_id_and_member_type", unique: true
    t.index ["project_id"], name: "index_memberships_on_project_id"
  end

  create_table "mpk_numbers", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "key"
    t.string "name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.date "expires_on"
    t.index ["expires_on"], name: "index_mpk_numbers_on_expires_on"
    t.index ["key"], name: "index_mpk_numbers_on_key", unique: true
  end

  create_table "mpk_position_versions", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "allocation_version_id"
    t.integer "mpk_number_id"
    t.integer "amount"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["allocation_version_id"], name: "index_mpk_position_versions_on_allocation_version_id"
    t.index ["mpk_number_id"], name: "index_mpk_position_versions_on_mpk_number_id"
  end

  create_table "mpk_positions", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "mpk_number_id"
    t.integer "invoice_id"
    t.integer "amount"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "project_id"
    t.index ["invoice_id"], name: "index_mpk_positions_on_invoice_id"
    t.index ["mpk_number_id"], name: "index_mpk_positions_on_mpk_number_id"
    t.index ["project_id"], name: "index_mpk_positions_on_project_id"
  end

  create_table "notifications", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "subject_id"
    t.string "subject_type"
    t.integer "user_id"
    t.integer "state", default: 0, null: false
    t.text "title"
    t.text "details"
    t.string "subject_sgid"
    t.datetime "last_sent_at", precision: nil
    t.text "email_dispatches"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "origin"
    t.index ["subject_type", "subject_id"], name: "index_notifications_on_subject_type_and_subject_id"
    t.index ["user_id"], name: "index_notifications_on_user_id"
  end

  create_table "oauth_access_grants", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "resource_owner_id", null: false
    t.bigint "application_id", null: false
    t.string "token", null: false
    t.integer "expires_in", null: false
    t.text "redirect_uri", null: false
    t.string "scopes", default: "", null: false
    t.datetime "created_at", precision: nil, null: false
    t.datetime "revoked_at", precision: nil
    t.index ["application_id"], name: "index_oauth_access_grants_on_application_id"
    t.index ["resource_owner_id"], name: "index_oauth_access_grants_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_grants_on_token", unique: true
  end

  create_table "oauth_access_tokens", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "resource_owner_id"
    t.bigint "application_id"
    t.string "token", null: false
    t.string "refresh_token"
    t.integer "expires_in"
    t.string "scopes"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "revoked_at", precision: nil
    t.string "previous_refresh_token", default: "", null: false
    t.index ["application_id"], name: "index_oauth_access_tokens_on_application_id"
    t.index ["refresh_token"], name: "index_oauth_access_tokens_on_refresh_token", unique: true
    t.index ["resource_owner_id"], name: "index_oauth_access_tokens_on_resource_owner_id"
    t.index ["token"], name: "index_oauth_access_tokens_on_token", unique: true
  end

  create_table "oauth_applications", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.string "name", null: false
    t.string "uid", null: false
    t.string "secret", null: false
    t.text "redirect_uri", null: false
    t.string "scopes", default: "", null: false
    t.boolean "confidential", default: true, null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["uid"], name: "index_oauth_applications_on_uid", unique: true
  end

  create_table "old_passwords", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "encrypted_password", null: false
    t.string "password_archivable_type", null: false
    t.integer "password_archivable_id", null: false
    t.datetime "created_at", precision: nil
    t.index ["password_archivable_type", "password_archivable_id"], name: "index_password_archivable"
  end

  create_table "payment_mpk_positions", id: :integer, charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "mpk_number_id"
    t.integer "payment_id"
    t.integer "amount"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.bigint "project_id"
    t.index ["mpk_number_id"], name: "index_payment_mpk_positions_on_mpk_number_id"
    t.index ["payment_id"], name: "index_payment_mpk_positions_on_payment_id"
    t.index ["project_id"], name: "index_payment_mpk_positions_on_project_id"
  end

  create_table "payment_schedules", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "project_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["project_id"], name: "index_payment_schedules_on_project_id"
  end

  create_table "payments", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "payment_schedule_id"
    t.date "issued_on"
    t.integer "predicted_amount"
    t.text "description"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "remind_at", precision: nil
    t.boolean "cyclic", default: false
    t.integer "originator_id"
    t.date "ends_on"
    t.integer "cycle_length"
    t.datetime "deleted_at", precision: nil
    t.integer "currency", default: 0
    t.date "sell_date"
    t.datetime "first_remind_at", precision: nil
    t.integer "kind", default: 0, null: false
    t.index ["deleted_at"], name: "index_payments_on_deleted_at"
    t.index ["ends_on"], name: "index_payments_on_ends_on"
    t.index ["originator_id"], name: "index_payments_on_originator_id"
    t.index ["payment_schedule_id"], name: "index_payments_on_payment_schedule_id"
  end

  create_table "pending_payments", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "payment_id", null: false
    t.integer "project_id", null: false
    t.integer "mpk_number_id", null: false
    t.integer "amount", null: false
    t.date "issued_on", null: false
    t.integer "currency", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "invoice_id", null: false
    t.index ["invoice_id"], name: "index_pending_payments_on_invoice_id"
    t.index ["mpk_number_id"], name: "index_pending_payments_on_mpk_number_id"
    t.index ["payment_id"], name: "index_pending_payments_on_payment_id"
    t.index ["project_id"], name: "index_pending_payments_on_project_id"
  end

  create_table "positions", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "project_agreements", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "project_id"
    t.integer "company_id"
    t.boolean "business_to_business"
    t.text "content", size: :medium
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "name"
    t.string "confirmation_button_text", default: "Accept", null: false
    t.integer "state", default: 0
    t.datetime "published_at", precision: nil
    t.index ["company_id"], name: "index_project_agreements_on_company_id"
    t.index ["name"], name: "index_project_agreements_on_name"
    t.index ["project_id"], name: "index_project_agreements_on_project_id"
  end

  create_table "projects", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.string "identifier"
    t.string "account_number"
    t.integer "parent_id"
    t.boolean "inherit_members", default: true
    t.boolean "public", default: false
    t.integer "company_id"
    t.boolean "personal_data"
    t.boolean "responsibility_list"
    t.integer "status", default: 0
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.boolean "owncloud"
    t.boolean "phabricator"
    t.string "homepage"
    t.bigint "gid_number"
    t.integer "lft", null: false
    t.integer "rgt", null: false
    t.boolean "edit_locked", default: false
    t.integer "redmine_id"
    t.string "code_name"
    t.boolean "chat", default: false
    t.boolean "jenkins", default: false
    t.boolean "payment_schedule_required", default: false
    t.date "starts_on"
    t.integer "currency", default: 0
    t.integer "days_to_payment"
    t.datetime "payment_schedule_remind_at", precision: nil
    t.integer "client_id"
    t.integer "responsible_id"
    t.string "client_division_name"
    t.string "street"
    t.string "additional_address"
    t.string "city"
    t.string "postcode"
    t.string "country"
    t.string "client_name"
    t.integer "author_id"
    t.boolean "docs_cloud", default: false
    t.string "street_number"
    t.string "apartment"
    t.string "post"
    t.string "voivodeship"
    t.string "district"
    t.string "community"
    t.integer "accounting_number_id"
    t.integer "bank_account_id"
    t.boolean "invoice_attachments_required", default: false, null: false
    t.boolean "idle", default: false
    t.boolean "sla", default: false
    t.date "sla_start_date"
    t.date "sla_end_date"
    t.bigint "click_up_id"
    t.boolean "cooperative_project", default: false
    t.index ["accounting_number_id"], name: "index_projects_on_accounting_number_id"
    t.index ["bank_account_id"], name: "index_projects_on_bank_account_id"
    t.index ["click_up_id"], name: "index_projects_on_click_up_id", unique: true
    t.index ["client_id"], name: "index_projects_on_client_id"
    t.index ["company_id"], name: "index_projects_on_company_id"
    t.index ["gid_number"], name: "index_projects_on_gid_number", unique: true
    t.index ["identifier"], name: "index_projects_on_identifier", unique: true
    t.index ["lft"], name: "index_projects_on_lft"
    t.index ["parent_id"], name: "index_projects_on_parent_id"
    t.index ["payment_schedule_remind_at"], name: "index_projects_on_payment_schedule_remind_at"
    t.index ["redmine_id"], name: "index_projects_on_redmine_id"
    t.index ["responsible_id"], name: "index_projects_on_responsible_id"
    t.index ["rgt"], name: "index_projects_on_rgt"
  end

  create_table "public_keys", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "identifier"
    t.string "fingerprint"
    t.text "key"
    t.integer "user_id"
    t.integer "redmine_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["user_id"], name: "index_public_keys_on_user_id"
  end

  create_table "question_answers", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "survey_answer_id"
    t.string "question_content"
    t.text "content"
    t.integer "numeric_content"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["survey_answer_id"], name: "index_question_answers_on_survey_answer_id"
  end

  create_table "registry_activities", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.text "description", null: false
    t.text "activity", null: false
    t.integer "administrator_type", null: false
    t.string "administrator_type_custom"
    t.integer "co_administrator_type", null: false
    t.string "co_administrator_type_custom"
    t.text "data_processing_goals", null: false
    t.text "legal_basis", null: false
    t.text "subjects_categories", null: false
    t.text "personal_data_categories", null: false
    t.text "categories_of_recipients", null: false
    t.text "transfer_to_third_country", null: false
    t.text "planned_deletion_dates", null: false
    t.text "security_measures", null: false
    t.text "creation_date", null: false
    t.integer "created_by_id"
    t.integer "state", default: 0, null: false
    t.date "expiration_date"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_by_id"], name: "index_registry_activities_on_created_by_id"
  end

  create_table "remote_work_periods", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.date "starts_on"
    t.date "ends_on"
    t.integer "user_id", null: false
    t.integer "state"
    t.datetime "accepted_at", precision: nil
    t.datetime "rejected_at", precision: nil
    t.integer "examiner_id"
    t.string "examiner_comment"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "place_of_work"
    t.datetime "notified_at", precision: nil
    t.index ["ends_on"], name: "index_remote_work_periods_on_ends_on"
    t.index ["notified_at"], name: "index_remote_work_periods_on_notified_at"
    t.index ["starts_on"], name: "index_remote_work_periods_on_starts_on"
    t.index ["state"], name: "index_remote_work_periods_on_state"
    t.index ["user_id"], name: "index_remote_work_periods_on_user_id"
  end

  create_table "resource_time_entries", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.string "type", null: false
    t.integer "project_id", null: false
    t.integer "user_id", null: false
    t.bigint "issue_id"
    t.date "date_from", null: false
    t.date "date_to"
    t.decimal "hours", precision: 6, scale: 2, null: false
    t.string "activity"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["issue_id"], name: "index_resource_time_entries_on_issue_id"
    t.index ["project_id"], name: "index_resource_time_entries_on_project_id"
    t.index ["user_id"], name: "index_resource_time_entries_on_user_id"
  end

  create_table "revenue_accounts", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "key"
    t.string "name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["key"], name: "index_revenue_accounts_on_key", unique: true
  end

  create_table "risk_analyses", charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.text "name", null: false
    t.integer "company_id", null: false
    t.bigint "registry_activity_id", null: false
    t.text "creation_date", null: false
    t.integer "property", null: false
    t.text "danger", null: false
    t.text "vulnerability_description", null: false
    t.text "security", null: false
    t.integer "probability", null: false
    t.integer "effect", null: false
    t.integer "risk", null: false
    t.integer "data_processing_impact_assessment", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_risk_analyses_on_company_id"
    t.index ["registry_activity_id"], name: "index_risk_analyses_on_registry_activity_id"
  end

  create_table "roles", id: :integer, charset: "utf8", collation: "utf8_polish_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.integer "redmine_id"
    t.boolean "edit_locked", default: false
    t.text "permissions"
    t.boolean "ldap", default: false
    t.boolean "pm", default: false
    t.boolean "docs_ldap", default: false
    t.boolean "responsible", default: false
    t.boolean "chat_ldap", default: false
    t.boolean "assets_visible", default: false, null: false
    t.boolean "cluster_keys_visible", default: false
    t.boolean "send_holiday_request_notification", default: true
    t.index ["redmine_id"], name: "index_roles_on_redmine_id", unique: true
  end

  create_table "scheduled_payments", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "payment_id", null: false
    t.integer "project_id", null: false
    t.integer "mpk_number_id"
    t.integer "amount", null: false
    t.date "issued_on", null: false
    t.integer "currency", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["mpk_number_id"], name: "index_scheduled_payments_on_mpk_number_id"
    t.index ["payment_id"], name: "index_scheduled_payments_on_payment_id"
    t.index ["project_id"], name: "index_scheduled_payments_on_project_id"
  end

  create_table "settings", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "var", null: false
    t.text "value"
    t.integer "thing_id"
    t.string "thing_type", limit: 30
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["thing_type", "thing_id", "var"], name: "index_settings_on_thing_type_and_thing_id_and_var", unique: true
  end

  create_table "snapshot_items", id: :integer, charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.integer "snapshot_id", null: false
    t.string "item_type", null: false
    t.integer "item_id", null: false
    t.text "object", null: false
    t.datetime "created_at", precision: nil, null: false
    t.string "child_group_name"
    t.index ["item_type", "item_id"], name: "index_snapshot_items_on_item_type_and_item_id"
    t.index ["snapshot_id"], name: "index_snapshot_items_on_snapshot_id"
  end

  create_table "snapshots", id: :integer, charset: "utf8mb4", collation: "utf8mb4_general_ci", force: :cascade do |t|
    t.string "item_type", null: false
    t.integer "item_id", null: false
    t.string "identifier", null: false
    t.string "user_type"
    t.integer "user_id"
    t.text "metadata"
    t.datetime "created_at", precision: nil, null: false
    t.index ["identifier"], name: "index_snapshots_on_identifier"
    t.index ["item_type", "item_id"], name: "index_snapshots_on_item_type_and_item_id"
    t.index ["user_type", "user_id"], name: "index_snapshots_on_user_type_and_user_id"
  end

  create_table "survey_answers", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "user_id"
    t.integer "evaluation_id"
    t.integer "state", default: 0
    t.integer "kind"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["evaluation_id"], name: "index_survey_answers_on_evaluation_id"
    t.index ["kind"], name: "index_survey_answers_on_kind"
    t.index ["state"], name: "index_survey_answers_on_state"
    t.index ["user_id"], name: "index_survey_answers_on_user_id"
  end

  create_table "survey_questions", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "survey_id"
    t.integer "kind"
    t.string "content"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.index ["survey_id"], name: "index_survey_questions_on_survey_id"
  end

  create_table "surveys", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "name"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
  end

  create_table "training_budgets", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "year"
    t.integer "department_id"
    t.decimal "amount", precision: 10, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["department_id"], name: "index_training_budgets_on_department_id"
    t.index ["year", "department_id"], name: "index_training_budgets_on_year_and_department_id", unique: true
  end

  create_table "training_requests", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "state", default: 0
    t.integer "kind"
    t.string "provider"
    t.date "starts_on"
    t.date "ends_on"
    t.integer "place"
    t.integer "mode"
    t.text "description"
    t.decimal "tickets_price", precision: 10, scale: 2
    t.decimal "transportation_price", precision: 10, scale: 2
    t.decimal "accommodation_price", precision: 10, scale: 2
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "notified_at"
    t.index ["ends_on"], name: "index_training_requests_on_ends_on"
    t.index ["kind"], name: "index_training_requests_on_kind"
    t.index ["notified_at"], name: "index_training_requests_on_notified_at"
    t.index ["starts_on"], name: "index_training_requests_on_starts_on"
    t.index ["state"], name: "index_training_requests_on_state"
    t.index ["user_id"], name: "index_training_requests_on_user_id"
  end

  create_table "user_contracts", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "agreement_type"
    t.date "starts_on"
    t.date "ends_on"
    t.integer "month_notice_period"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_user_contracts_on_user_id"
  end

  create_table "user_entry_cards", charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.integer "user_id", null: false
    t.integer "card_number"
    t.date "starts_on"
    t.date "ends_on"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["card_number"], name: "index_user_entry_cards_on_card_number"
    t.index ["ends_on"], name: "index_user_entry_cards_on_ends_on"
    t.index ["starts_on"], name: "index_user_entry_cards_on_starts_on"
    t.index ["user_id"], name: "index_user_entry_cards_on_user_id"
  end

  create_table "user_global_roles", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "user_id"
    t.integer "global_role_id"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.datetime "deleted_at", precision: nil
    t.index ["deleted_at"], name: "index_user_global_roles_on_deleted_at"
    t.index ["global_role_id"], name: "index_user_global_roles_on_global_role_id"
    t.index ["user_id"], name: "index_user_global_roles_on_user_id"
  end

  create_table "users", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.string "provider", default: "email", null: false
    t.string "uid", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "unconfirmed_email"
    t.string "username"
    t.string "first_name"
    t.string "last_name"
    t.string "image"
    t.string "email"
    t.string "legacy_password"
    t.string "legacy_salt"
    t.text "tokens"
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "state", default: 0
    t.integer "redmine_id"
    t.boolean "edit_locked", default: false
    t.integer "failed_attempts", default: 0, null: false
    t.string "unlock_token"
    t.datetime "locked_at", precision: nil
    t.datetime "password_changed_at", precision: nil
    t.text "hashed_passwords"
    t.integer "company_id"
    t.string "unconfirmed_username"
    t.string "first_password"
    t.string "uid_number"
    t.string "gid_number"
    t.string "home_path"
    t.string "login_shell"
    t.boolean "cloud", default: false
    t.integer "absence_quota"
    t.text "absence_quota_received_in_balance_in_years"
    t.integer "absence_balance"
    t.boolean "redmine", default: true
    t.boolean "chat", default: false
    t.boolean "system", default: false
    t.text "absence_balance_holiday_requests"
    t.text "preferences"
    t.boolean "monitoring", default: false
    t.boolean "svn", default: false
    t.boolean "contract_of_employment", default: false
    t.integer "department_id"
    t.date "activates_on"
    t.boolean "docs_cloud", default: false
    t.integer "position_id"
    t.boolean "dismiss_onboarding", default: true
    t.boolean "has_approvals", default: false
    t.boolean "remote", default: false
    t.integer "sick_absence_balance", default: 10
    t.integer "sick_absence_quota", default: 10
    t.boolean "time_reports_not_required", default: false
    t.text "profile_comment"
    t.boolean "card_holder", default: false
    t.boolean "part_time", default: false
    t.string "activation_token"
    t.datetime "inactivity_notified_at", precision: nil
    t.boolean "activity_validation_disabled", default: false, null: false
    t.boolean "remote_allowed"
    t.integer "remote_yearly_limit"
    t.bigint "click_up_id"
    t.boolean "admin_click_up", default: false
    t.index ["click_up_id"], name: "index_users_on_click_up_id", unique: true
    t.index ["company_id"], name: "index_users_on_company_id"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["department_id"], name: "index_users_on_department_id"
    t.index ["email"], name: "index_users_on_email"
    t.index ["password_changed_at"], name: "index_users_on_password_changed_at"
    t.index ["redmine_id"], name: "index_users_on_redmine_id", unique: true
    t.index ["remote_allowed"], name: "index_users_on_remote_allowed"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["state"], name: "index_users_on_state"
    t.index ["uid", "provider"], name: "index_users_on_uid_and_provider", unique: true
    t.index ["uid_number"], name: "index_users_on_uid_number", unique: true
    t.index ["unlock_token"], name: "index_users_on_unlock_token", unique: true
  end

  create_table "versions", id: :integer, charset: "utf8mb3", collation: "utf8mb3_general_ci", force: :cascade do |t|
    t.string "item_type", limit: 191, null: false
    t.integer "item_id", null: false
    t.string "event", null: false
    t.string "whodunnit"
    t.text "object", size: :long
    t.datetime "created_at", precision: nil
    t.text "object_changes", size: :long
    t.string "comment"
    t.index ["item_type", "item_id"], name: "index_versions_on_item_type_and_item_id"
  end

  create_table "wifi_tokens", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.integer "user_id"
    t.string "purpose"
    t.string "token"
    t.integer "hours_valid"
    t.datetime "expires_at", precision: nil
    t.index ["expires_at"], name: "index_wifi_tokens_on_expires_at"
  end

  create_table "workers_reports", id: :integer, charset: "utf8mb3", collation: "utf8mb3_polish_ci", force: :cascade do |t|
    t.integer "month"
    t.integer "year"
    t.integer "user_id"
    t.integer "company_id"
    t.integer "state", default: 0
    t.text "file_data"
    t.date "generated_on"
    t.datetime "created_at", precision: nil, null: false
    t.datetime "updated_at", precision: nil, null: false
    t.string "type"
    t.index ["company_id"], name: "index_workers_reports_on_company_id"
    t.index ["user_id"], name: "index_workers_reports_on_user_id"
  end

  add_foreign_key "absences", "holiday_requests"
  add_foreign_key "absences", "users"
  add_foreign_key "accounting_numbers", "companies"
  add_foreign_key "accounting_numbers", "users"
  add_foreign_key "agreement_companies", "agreements", on_delete: :cascade
  add_foreign_key "agreement_companies", "companies", on_delete: :cascade
  add_foreign_key "agreement_departments", "agreements", on_delete: :cascade
  add_foreign_key "agreement_departments", "departments", on_delete: :cascade
  add_foreign_key "allocation_versions", "invoices"
  add_foreign_key "answer_comments", "evaluations"
  add_foreign_key "approvals", "users", on_delete: :cascade
  add_foreign_key "assets", "kubernetes_clusters"
  add_foreign_key "assets", "projects", on_delete: :cascade
  add_foreign_key "assets", "users"
  add_foreign_key "bank_accounts", "companies"
  add_foreign_key "bios", "users"
  add_foreign_key "calendar_months", "users"
  add_foreign_key "client_addresses", "clients"
  add_foreign_key "completed_payments", "mpk_numbers"
  add_foreign_key "completed_payments", "payments"
  add_foreign_key "completed_payments", "projects"
  add_foreign_key "contractors", "users", column: "created_by_id"
  add_foreign_key "contractors", "users", on_delete: :cascade
  add_foreign_key "cost_allocation_template_positions", "accounting_numbers"
  add_foreign_key "cost_allocation_template_positions", "cost_allocation_templates"
  add_foreign_key "cost_allocation_template_positions", "departments"
  add_foreign_key "cost_allocation_templates", "users"
  add_foreign_key "cost_invoice_acceptances", "cost_invoices"
  add_foreign_key "cost_invoice_acceptances", "users"
  add_foreign_key "cost_invoice_positions", "cost_invoices"
  add_foreign_key "cost_invoices", "companies"
  add_foreign_key "cost_invoices", "contractors"
  add_foreign_key "cost_invoices", "users"
  add_foreign_key "cost_invoices", "users", column: "cash_payer_id"
  add_foreign_key "cost_projects", "cost_invoices"
  add_foreign_key "cost_projects", "departments"
  add_foreign_key "cost_projects", "projects"
  add_foreign_key "departments", "mpk_numbers"
  add_foreign_key "departments", "roles"
  add_foreign_key "docs_file_comments", "docs_files"
  add_foreign_key "docs_file_comments", "users", column: "created_by_id"
  add_foreign_key "docs_files", "projects"
  add_foreign_key "email_aliases", "users"
  add_foreign_key "evaluation_additional_users", "evaluations"
  add_foreign_key "evaluation_answers", "evaluation_iterations"
  add_foreign_key "evaluation_answers", "users", column: "respondent_id"
  add_foreign_key "evaluation_iteration_assignments", "evaluation_iterations"
  add_foreign_key "evaluation_iteration_assignments", "users"
  add_foreign_key "evaluation_iterations", "evaluations"
  add_foreign_key "evaluations", "surveys", column: "main_survey_id"
  add_foreign_key "evaluations", "users"
  add_foreign_key "external_costs", "users", column: "created_by_id"
  add_foreign_key "group_memberships", "groups"
  add_foreign_key "group_memberships", "users"
  add_foreign_key "inventory_items", "companies"
  add_foreign_key "inventory_items", "users"
  add_foreign_key "invoice_documents", "invoices"
  add_foreign_key "invoice_positions", "invoices"
  add_foreign_key "invoice_required_attachments", "attachments"
  add_foreign_key "invoice_required_attachments", "invoices"
  add_foreign_key "invoices", "client_addresses"
  add_foreign_key "invoices", "payments"
  add_foreign_key "invoices", "revenue_accounts"
  add_foreign_key "issues", "projects"
  add_foreign_key "links", "bios"
  add_foreign_key "membership_roles", "memberships"
  add_foreign_key "membership_roles", "roles"
  add_foreign_key "memberships", "projects"
  add_foreign_key "mpk_position_versions", "allocation_versions"
  add_foreign_key "mpk_position_versions", "mpk_numbers"
  add_foreign_key "mpk_positions", "invoices"
  add_foreign_key "mpk_positions", "mpk_numbers"
  add_foreign_key "notifications", "users"
  add_foreign_key "oauth_access_grants", "oauth_applications", column: "application_id"
  add_foreign_key "oauth_access_grants", "users", column: "resource_owner_id"
  add_foreign_key "oauth_access_tokens", "oauth_applications", column: "application_id"
  add_foreign_key "oauth_access_tokens", "users", column: "resource_owner_id"
  add_foreign_key "payment_mpk_positions", "mpk_numbers"
  add_foreign_key "payment_mpk_positions", "payments"
  add_foreign_key "payment_schedules", "projects"
  add_foreign_key "payments", "payment_schedules", on_delete: :cascade
  add_foreign_key "pending_payments", "mpk_numbers"
  add_foreign_key "pending_payments", "payments"
  add_foreign_key "pending_payments", "projects"
  add_foreign_key "project_agreements", "companies"
  add_foreign_key "project_agreements", "projects"
  add_foreign_key "projects", "clients"
  add_foreign_key "projects", "companies"
  add_foreign_key "public_keys", "users"
  add_foreign_key "question_answers", "survey_answers"
  add_foreign_key "registry_activities", "users", column: "created_by_id"
  add_foreign_key "remote_work_periods", "users"
  add_foreign_key "resource_time_entries", "issues"
  add_foreign_key "resource_time_entries", "projects"
  add_foreign_key "resource_time_entries", "users"
  add_foreign_key "risk_analyses", "companies"
  add_foreign_key "risk_analyses", "registry_activities"
  add_foreign_key "scheduled_payments", "mpk_numbers"
  add_foreign_key "scheduled_payments", "payments"
  add_foreign_key "scheduled_payments", "projects"
  add_foreign_key "survey_answers", "evaluations"
  add_foreign_key "survey_answers", "users"
  add_foreign_key "survey_questions", "surveys"
  add_foreign_key "training_budgets", "departments"
  add_foreign_key "training_requests", "users"
  add_foreign_key "user_contracts", "users"
  add_foreign_key "user_entry_cards", "users"
  add_foreign_key "user_global_roles", "global_roles"
  add_foreign_key "workers_reports", "companies"
  add_foreign_key "workers_reports", "users"

  create_view "costs_forecasts", sql_definition: <<-SQL
      with currency_map as (select 0 AS `currency_id`,'PLN' AS `currency` union all select 1 AS `currency_id`,'EUR' AS `currency` union all select 2 AS `currency_id`,'USD' AS `currency` union all select 3 AS `currency_id`,'GBP' AS `currency` union all select 4 AS `currency_id`,'CHF' AS `currency`)select `accounting_numbers`.`number` AS `project_no`,`mpk_numbers`.`key` AS `mpk_number`,`companies`.`name` AS `company`,`external_costs`.`cost_date` AS `date`,NULL AS `due_date`,NULL AS `invoice_number`,`contractors`.`name` AS `contractor`,`external_costs`.`amount` / 100.0 AS `amount`,`currency_map`.`currency` AS `currency`,'External' AS `cost_type` from ((((((`external_costs` join `projects` on(`external_costs`.`project_id` = `projects`.`id`)) join `accounting_numbers` on(`projects`.`accounting_number_id` = `accounting_numbers`.`id`)) join `mpk_numbers` on(`external_costs`.`mpk_number_id` = `mpk_numbers`.`id`)) join `companies` on(`projects`.`company_id` = `companies`.`id`)) join `contractors` on(`external_costs`.`contractor_id` = `contractors`.`id`)) join `currency_map` on(`currency_map`.`currency_id` = `external_costs`.`currency`))
  SQL
  create_view "payments_forecasts", sql_definition: <<-SQL
      with currency_map as (select 0 AS `currency_id`,'PLN' AS `currency` union all select 1 AS `currency_id`,'EUR' AS `currency` union all select 2 AS `currency_id`,'USD' AS `currency` union all select 3 AS `currency_id`,'GBP' AS `currency` union all select 4 AS `currency_id`,'CHF' AS `currency`)select `accounting_numbers`.`number` AS `project_no`,`mpk_numbers`.`key` AS `mpk`,`companies`.`name` AS `company`,coalesce(`invoices`.`sell_date`,`payments`.`sell_date`) AS `sell_date`,`invoices`.`number` AS `invoice_number`,coalesce(`client_addresses`.`name`,`clients`.`name`) AS `client_name`,round(`scheduled_payments`.`amount` / 100.0,2) AS `scheduled_amount`,`currency_map`.`currency` AS `currency`,round(`completed_payments`.`amount` / 100.0,2) AS `issued_amount`,round(`completed_payments`.`amount` - `scheduled_payments`.`amount`,2) AS `difference` from ((((((((((`scheduled_payments` join `projects` on(`scheduled_payments`.`project_id` = `projects`.`id`)) join `accounting_numbers` on(`projects`.`accounting_number_id` = `accounting_numbers`.`id`)) join `mpk_numbers` on(`scheduled_payments`.`mpk_number_id` = `mpk_numbers`.`id`)) join `companies` on(`projects`.`company_id` = `companies`.`id`)) join `payments` on(`scheduled_payments`.`payment_id` = `payments`.`id`)) left join `completed_payments` on(`scheduled_payments`.`mpk_number_id` = `completed_payments`.`mpk_number_id` and `scheduled_payments`.`payment_id` = `completed_payments`.`payment_id` and `scheduled_payments`.`project_id` = `completed_payments`.`project_id`)) left join `invoices` on(`completed_payments`.`invoice_id` = `invoices`.`id`)) join `clients` on(`projects`.`client_id` = `clients`.`id`)) join `currency_map` on(`scheduled_payments`.`currency` = `currency_map`.`currency_id`)) left join `client_addresses` on(`invoices`.`client_address_id` = `client_addresses`.`id`))
  SQL
  create_view "project_members", sql_definition: <<-SQL
      select distinct `accounting_numbers`.`id` AS `project_no`,concat(`users`.`first_name`,' ',`users`.`last_name`) AS `user`,`users`.`id` AS `user_id`,`roles`.`name` AS `role`,cast(`membership_roles`.`created_at` as date) AS `added` from (((((`membership_roles` join `memberships` on(`membership_roles`.`membership_id` = `memberships`.`id`)) join `roles` on(`membership_roles`.`role_id` = `roles`.`id`)) join `users` on(`memberships`.`member_id` = `users`.`id` and `memberships`.`member_type` = 'User')) join `projects` on(`memberships`.`project_id` = `projects`.`id`)) join `accounting_numbers` on(`projects`.`accounting_number_id` = `accounting_numbers`.`id`)) where `users`.`company_id` in (1,2) and `users`.`state` = 0
  SQL
  create_view "projects_bi", sql_definition: <<-SQL
      select `accounting_numbers`.`number` AS `project_no`,`projects`.`name` AS `project`,`companies`.`name` AS `company`,`clients`.`name` AS `client`,case when `accounting_numbers`.`overhead` = 1 then 'tak' else 'nie' end AS `overhead`,concat(`users`.`first_name`,' ',`users`.`last_name`) AS `business_owner` from ((((((((`accounting_numbers` join `projects` on(`accounting_numbers`.`id` = `projects`.`accounting_number_id`)) join `payment_schedules` on(`projects`.`id` = `payment_schedules`.`project_id`)) join `companies` on(`projects`.`company_id` = `companies`.`id`)) join `clients` on(`projects`.`client_id` = `clients`.`id`)) left join `memberships` on(`projects`.`id` = `memberships`.`project_id`)) left join `membership_roles` on(`membership_roles`.`membership_id` = `memberships`.`id`)) left join `roles` on(`membership_roles`.`role_id` = `roles`.`id`)) left join `users` on(`memberships`.`member_id` = `users`.`id` and `memberships`.`member_type` = 'User')) where `roles`.`name` = 'Uber Project Manager' and `users`.`state` = 0 group by `accounting_numbers`.`number`
  SQL
  create_view "reported_hours", sql_definition: <<-SQL
      select `users`.`id` AS `user_id`,concat(`users`.`first_name`,' ',`users`.`last_name`) AS `name`,`accounting_numbers`.`number` AS `project_no`,`resource_time_entries`.`date_from` AS `date`,`resource_time_entries`.`hours` AS `hours`,`mpk_numbers`.`key` AS `mpk` from (((((`resource_time_entries` join `users` on(`resource_time_entries`.`user_id` = `users`.`id`)) join `projects` on(`resource_time_entries`.`project_id` = `projects`.`id`)) join `accounting_numbers` on(`projects`.`accounting_number_id` = `accounting_numbers`.`id`)) join `departments` on(`users`.`department_id` = `departments`.`id`)) join `mpk_numbers` on(`departments`.`mpk_number_id` = `mpk_numbers`.`id`)) where `resource_time_entries`.`type` = 'SpentTimeEntry'
  SQL
end
