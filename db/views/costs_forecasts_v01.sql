WITH currency_map AS (
  SELECT 0 AS currency_id, 'PLN' AS currency
  UNION ALL
  SELECT 1 AS currency_id, 'EUR' AS currency
  UNION ALL
  SELECT 2 AS currency_id, 'USD' AS currency
  UNION ALL
  SELECT 3 AS currency_id, 'GBP' AS currency
  UNION ALL
  SELECT 4 AS currency_id, 'CHF' AS currency
), external_costs AS (
  SELECT accounting_numbers.number AS project_no, mpk_numbers.key AS mpk_number,
    companies.name AS company, external_costs.cost_date AS date,
    NULL as due_date, NULL AS invoice_number,
    contractors.name AS contractor,
    external_costs.amount / 100.0 AS amount, 'PLN' AS currency,
    'External' AS cost_type
  FROM external_costs
  INNER JOIN projects ON external_costs.project_id = projects.id
  INNER JOIN accounting_numbers ON projects.accounting_number_id = accounting_numbers.id
  INNER JOIN mpk_numbers ON external_costs.mpk_number_id = mpk_numbers.id
  INNER JOIN companies ON projects.company_id = companies.id
  INNER JOIN contractors ON external_costs.contractor_id = contractors.id
), dms_costs AS (
  SELECT accounting_numbers.number AS project_no, mpk_numbers.key AS mpk_number,
    companies.name AS company, cost_invoices.invoice_date AS date,
    cost_invoices.due_date AS due_date, cost_invoices.number AS invoice_number,
    contractors.name AS contractor, cost_projects.amount AS amount,
    currency_map.currency AS currency,
    "External" AS cost_type
  FROM cost_projects
  INNER JOIN cost_invoices ON cost_invoices.id = cost_projects.cost_invoice_id
  INNER JOIN accounting_numbers ON accounting_numbers.id = cost_projects.accounting_number_id
  INNER JOIN departments ON departments.id = cost_projects.department_id
  INNER JOIN mpk_numbers ON mpk_numbers.id = departments.mpk_number_id
  INNER JOIN companies ON companies.id = cost_invoices.company_id
  INNER JOIN contractors ON contractors.id = cost_invoices.contractor_id
  INNER JOIN currency_map ON currency_map.currency_id = cost_invoices.currency
  WHERE cost_invoices.type = "Dms::CostInvoice" AND cost_invoices.state = 4
), b2b_costs AS (
    SELECT accounting_numbers.number AS project_no, mpk_numbers.key AS mpk_number,
    companies.name AS company, cost_invoices.invoice_date AS date,
    cost_invoices.due_date AS due_date, cost_invoices.number AS invoice_number,
    contractors.name AS contractor, cost_projects.amount AS amount,
    currency_map.currency AS currency,
    "Direct" AS cost_type
  FROM cost_projects
  INNER JOIN cost_invoices ON cost_invoices.id = cost_projects.cost_invoice_id
  INNER JOIN accounting_numbers ON accounting_numbers.id = cost_projects.accounting_number_id
  INNER JOIN contractors ON contractors.id = cost_invoices.contractor_id
  INNER JOIN users ON users.id = contractors.user_id
  INNER JOIN mpk_numbers ON mpk_numbers.id = users.mpk_number_id
  INNER JOIN companies ON companies.id = cost_invoices.company_id
  INNER JOIN currency_map ON currency_map.currency_id = cost_invoices.currency
  WHERE cost_invoices.type = "B2B::CostInvoice" AND cost_invoices.state = 1
)

SELECT * FROM external_costs UNION ALL SELECT * FROM dms_costs UNION ALL SELECT * FROM b2b_costs
