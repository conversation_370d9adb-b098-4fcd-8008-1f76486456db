namespace :imperator do
  task create_user_members_from_groups_in_projects: :environment do
    group_memberships = Membership.where(member_type: 'Group').includes(member: :users)
    group_memberships.each do |group_membership|
      users_in_group = group_membership.member.users
      users_in_group.each do |user|
        existing_membership = Membership.find_by(member_id: user.id,
                                                 member_type: 'User',
                                                 project_id: group_membership.project_id)
        if existing_membership
          group_membership.membership_roles.each do |group_membership_role|
            existing_membership.membership_roles << MembershipRole.new(role_id: group_membership_role.role_id,
                                                                      inherited_from_id: group_membership_role.id)
          end
        else
          Membership.create!(member_id: user.id, member_type: 'User',
                             project_id: group_membership.project_id) do |membership|
            group_membership.membership_roles.each do |group_membership_role|
              membership.membership_roles << MembershipRole.new(role_id: group_membership_role.role_id,
                                                                inherited_from_id: group_membership_role.id)
            end
          end
        end
      end
    end
  end
end
