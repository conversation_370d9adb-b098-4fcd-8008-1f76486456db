namespace :imperator do
  task import_accounting_numbers: :environment do
    book = Spreadsheet.open(Rails.root.join('lista_numerow_ksiegowych.xls'))
    sheet = book.worksheet 0
    numbers = sheet.rows[3..-1]

    Project.where.not(account_number: nil).each do |project|
      number = project.read_attribute(:account_number)&.to_i

      xls_number = numbers.detect do |row|
        row.second.to_i == number
      end
      next unless xls_number

      company = Company.find_by name: xls_number[3]
      next unless company

      first_name, last_name = xls_number[2]&.split(' ')
      user = User.find_by(first_name: first_name, last_name: last_name)

      project.accounting_number = AccountingNumber.find_or_initialize_by(company: company,
                                                                         number: number)
      project.accounting_number.user = user if user
      project.accounting_number.description = xls_number[4]
      project.save!
    end
  end
end
