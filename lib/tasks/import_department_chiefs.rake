# encoding: UTF-8
namespace :imperator do
  # osoby bez dzialow:
  # User.active.where('users.company_id IS NOT NULL').select('users.id, users.last_name, users.first_name, COUNT(departments_users.user_id) as d_count').joins('LEFT JOIN departments_users ON departments_users.user_id = users.id').group('users.id').having('d_count = 0').to_a.map { |i| [i.last_name, i.first_name].join(' ') }.map { |i| puts i }; nil
  task import_department_chiefs: :environment do
    # @deprecated
=begin
    # NOTE: RUN THIS AFTER import_depertment_users.rake
    raise 'Comment this out if you want to run this on production! This will delete all departments!' if Rails.env.production?
    require 'csv'
    filename = Rails.root.join('docs/szefowie.csv')
    # Department.destroy_all # na razie zachowujemy stare dzialy, bo tylko do nich mamy przypisania userow
    CSV.foreach(filename, headers: true, col_sep: '|') do |row|
      row_hash = row.to_hash
      if Rails.env.development? && ENV['PRO_DATABASE'].to_s != 'true'
        company_id = nil # Company.where('lower(name) LIKE ?', "%#{row_hash['Firma'].to_s.strip}%").first!
        chief_id = User.all[0].id
        substitute_chief_id = User.all[1].id
        uber_chief_id = User.all[2].id
      else
        # company_id = nil # Company.where('lower(name) LIKE ?', "%#{row_hash['Firma'].to_s.strip}%").first!
        last_name = (row_hash['kierownik'] || row_hash['Kierownik']).to_s.strip.split(' ').last.to_s.strip
        puts last_name
next if last_name == 'Świerczyńska' && User.where(last_name: last_name).first.nil? # tymczasowo
next if last_name == 'Gortych' && User.where(last_name: last_name).first.nil? # tymczasowo
        chief_id = User.where(last_name: last_name).first!.id
        last_name = (row_hash['zastępca'] || row_hash['Zastępca']).to_s.strip.split(' ').last.to_s.strip
        puts last_name
        substitute_chief_id = last_name.presence && User.where(last_name: last_name).first!.id
        last_name = (row_hash['zatwierdza urlop kierownika'] || row_hash['Kto zatwierdza urlop kierownikowi']).to_s.strip.split(' ').last.to_s.strip
        puts last_name
        uber_chief_id = last_name.presence && User.where(last_name: last_name).first!.id
      end
      department = Department.create_with(
        # company_id: company_id,
        chief_id: chief_id,
        substitute_chief_id: substitute_chief_id,
        uber_chief_id: uber_chief_id
      ).find_or_create_by!(name: (row_hash['dział'] || row_hash['Dział']).to_s.strip)
      department.chief_id = chief_id
      department.substitute_chief_id = substitute_chief_id
      department.uber_chief_id = uber_chief_id
      department.save(validate: false) # skip name uniqueness per company
    end
    nil
=end
  end
end
