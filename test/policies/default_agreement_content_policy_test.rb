require 'test_helper'

class DefaultAgreementContentPolicyTest < PolicyAssertions::Test
  extend Minitest::Spec::DSL
  include UsersHelper

  let(:agreements_admin) do
    user = generate_user
    user.global_roles << global_roles(:global_role_agreements_admin)
    user
  end

  let(:global_admin) do
    user = generate_user
    user.global_roles << global_roles(:global_admin)
    user
  end

  let(:signed_in_user) do
    user = generate_user
    user.global_roles << global_roles(:global_user)
    user
  end

  let(:signed_in_user_with_agreements_approval) do
    user = generate_user
    user.global_roles << global_roles(:global_user)
    user
  end

  let(:nil_user) {nil}

  let(:record_model) {DefaultAgreementContent}
  let(:default_agreement_content) {default_agreement_contents(:one)}

  def test_index
    assert_permit agreements_admin, default_agreement_content
    assert_permit global_admin, default_agreement_content
    refute_permit signed_in_user_with_agreements_approval, default_agreement_content
    refute_permit nil_user, default_agreement_content
    refute_permit signed_in_user, default_agreement_content
  end

  def test_update
    assert_permit agreements_admin, default_agreement_content
    assert_permit global_admin, default_agreement_content
    refute_permit signed_in_user_with_agreements_approval, default_agreement_content
    refute_permit nil_user, default_agreement_content
    refute_permit signed_in_user, default_agreement_content
  end
end
