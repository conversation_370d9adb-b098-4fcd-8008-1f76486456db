---
http_interactions:
- request:
    method: post
    uri: http://local.non.3dart.com:3001/imperator_api/v1/users.json
    body:
      encoding: UTF-8
      string: '{"user":{"login":"mg<PERSON><PERSON>","mail":"mg<PERSON><PERSON>@artegence.com","firstname":"<PERSON><PERSON><PERSON><PERSON>","lastname":"<PERSON><PERSON><PERSON>","status":1,"auth_source_id":1,"group_ids":[]}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 201
      message: Created
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Location:
      - http://local.non.3dart.com:3001/users/1254
      Content-Type:
      - application/json; charset=utf-8
      Etag:
      - W/"02a1cd44407039076997459a9b2a552e"
      Cache-Control:
      - max-age=0, private, must-revalidate
      Set-Cookie:
      - request_method=POST; path=/
      X-Request-Id:
      - c5d93f41-9678-437f-a2f5-e5d673bc8252
      X-Runtime:
      - '0.171421'
      Content-Length:
      - '290'
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        eyJ1c2VyIjp7ImlkIjoxMjU0LCJsb2dpbiI6Im1ncmFib3dza2kiLCJmaXJz
        dG5hbWUiOiJNacWCb3N6IiwibGFzdG5hbWUiOiJHcmFib3dza2kiLCJtYWls
        ************************************************************
        MTYtMTItMDFUMTU6MDk6MjArMDE6MDAiLCJhcGlfa2V5IjoiMWM5Mzk4MDFl
        MmU0ZWJlYmI2MzQwMGYxNDJjNzNkODM0ZWNjMDYyMyIsInN0YXR1cyI6MSwi
        Y3VzdG9tX2ZpZWxkcyI6W3siaWQiOjQsIm5hbWUiOiJUZWFtIFN0YXRzIiwi
        dmFsdWUiOiIyIHdlZWtzIn1dfX0=
    http_version: 
  recorded_at: Thu, 01 Dec 2016 14:09:20 GMT
- request:
    method: put
    uri: http://local.non.3dart.com:3001/imperator_api/v1/users/1254.json
    body:
      encoding: UTF-8
      string: '{"user":{"login":"mgrabowski","mail":"<EMAIL>","firstname":"Miłosz","lastname":"Grabowski","status":1,"auth_source_id":1,"group_ids":[]}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Cache-Control:
      - no-cache
      Set-Cookie:
      - request_method=PUT; path=/
      X-Request-Id:
      - 3cf8c602-0278-4ac8-b682-66bff62d6497
      X-Runtime:
      - '0.051317'
      Content-Length:
      - '0'
    body:
      encoding: UTF-8
      string: ''
    http_version: 
  recorded_at: Thu, 01 Dec 2016 14:09:21 GMT
recorded_with: VCR 3.0.3
