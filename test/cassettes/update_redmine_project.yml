---
http_interactions:
- request:
    method: put
    uri: http://local.non.3dart.com:3001/imperator_api/v1/projects/818.json
    body:
      encoding: UTF-8
      string: '{"project":{"name":"Edited project two","identifier":"mystringtwo","description":"MyText","homepage":null,"is_public":false,"parent_id":null,"inherit_members":false,"custom_field_values":{"5":"1235","14":"Efigence"}}}'
    headers:
      Content-Type:
      - application/json
      Accept:
      - application/json
      X-Redmine-Api-Key:
      - 2a015af0aaa2e0b525831b9c2f238b14311bf012
      X-Imperator-Api-Key:
      - 2ce03d6ea21775217f0ef4b8e56ce51abf86977a34270147b78210dc24632eda20f2b26fea440953cdeb2cb0fd6b82cbe2254a48f2b5d916db48e9851d9200d0
  response:
    status:
      code: 200
      message: OK
    headers:
      X-Frame-Options:
      - SAMEORIGIN
      X-Xss-Protection:
      - 1; mode=block
      X-Content-Type-Options:
      - nosniff
      Content-Type:
      - application/json; charset=utf-8
      Cache-Control:
      - no-cache
      Set-Cookie:
      - request_method=PUT; path=/
      X-Request-Id:
      - 7c7f1559-9331-463d-9132-d6c3dc9b5d76
      X-Runtime:
      - '0.147479'
      Transfer-Encoding:
      - chunked
    body:
      encoding: UTF-8
      string: ''
    http_version:
  recorded_at: Mon, 30 May 2016 12:03:37 GMT
recorded_with: VCR 3.0.1
