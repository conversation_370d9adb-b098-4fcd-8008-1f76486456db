require 'test_helper'

class CreateMembershipsTest < ActiveSupport::TestCase

  def setup
    valid_user_params = { first_name: '<PERSON>',
                          last_name: '<PERSON>',
                          activates_on: Time.zone.today,
                          company_id: companies(:two).id }
    @user = CreateUser.new.call(valid_user_params)
    @user.global_roles << global_roles(:global_user)
    role_ids = [roles(:mkalita_role).id]
    group_ids = [groups(:mkalita_group_alternative).id]
    @project = projects(:mkalita_project)
    @memberships_params = { role_ids: role_ids, group_ids: group_ids }
  end

  def test_call_on_new_membership
    new_role_ids = [roles(:unused_role).id]
    params = @memberships_params.merge(role_ids: new_role_ids)
    result = CreateMemberships.new.call(@project, params)
    assert_empty result.errors, result.errors.inspect
    assert_equal new_role_ids, result.role_ids
  end

  def test_call_on_existing_membership
    create_membership
    new_role_ids = [roles(:unused_role).id]
    params = @memberships_params.merge(role_ids: new_role_ids)
    result = CreateMemberships.new.call(@project, params)
    assert_empty result.errors, result.errors.inspect
    assert_equal new_role_ids, result.role_ids
  end

  private

  def create_membership
    @membership = CreateMemberships.new.call(@project, @memberships_params.dup)
    raise "could\'t CreateMembership: #{@membership.errors.inspect}" if @membership.errors.any?
  end
end
