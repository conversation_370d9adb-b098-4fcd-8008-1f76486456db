require 'test_helper'

class HolidaysReportGeneratorTest < ActiveSupport::TestCase
  test 'call works without errors' do
    user = users(:wik<PERSON>)
    scope = User.internal
    after = Time.zone.now.beginning_of_month
    before = Time.zone.now.end_of_month
    HolidayRequest.all.each(&:save)

    csv = CSV.new(HolidaysReportGenerator.new.call(scope, after, before))
    array = csv.to_a
    element = array.detect { |el| el.second == user.full_name }
    assert_equal user.absences.count.to_s, element.fifth
  end
end
