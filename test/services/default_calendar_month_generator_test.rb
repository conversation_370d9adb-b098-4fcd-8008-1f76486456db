require 'test_helper'

class DefaultCalendarMonthGeneratorTest < ActiveSupport::TestCase
  subject { DefaultCalendarMonthGenerator.new(year, month) }
  let(:year) { 2023 }
  let(:month) { 12 }

  test 'invalid month' do
    assert_raise ArgumentError do
      DefaultCalendarMonthGenerator.new(year, 0)
    end
  end

  test 'pl_full_time' do
    pl_full_time = subject.pl_full_time

    assert_nil pl_full_time.user
    assert_equal year, pl_full_time.year
    assert_equal month, pl_full_time.month
    assert_equal({ 1 => 8, 4 => 8, 5 => 8, 6 => 8, 7 => 8, 8 => 8,
                   11 => 8, 12 => 8, 13 => 8, 14 => 8, 15 => 8,
                   18 => 8, 19 => 8, 20 => 8, 21 => 8, 22 => 8,
                   27 => 8, 28 => 8, 29 => 8 }, pl_full_time.days)
    assert_equal 152, pl_full_time.total_hours
  end

  test 'pl_half_time' do
    pl_half_time = subject.pl_half_time

    assert_nil pl_half_time.user
    assert_equal year, pl_half_time.year
    assert_equal month, pl_half_time.month
    assert_equal({ 1 => 4, 4 => 4, 5 => 4, 6 => 4, 7 => 4, 8 => 4,
                   11 => 4, 12 => 4, 13 => 4, 14 => 4, 15 => 4,
                   18 => 4, 19 => 4, 20 => 4, 21 => 4, 22 => 4,
                   27 => 4, 28 => 4, 29 => 4 }, pl_half_time.days)
    assert_equal 76, pl_half_time.total_hours
  end

  test 'rb_full_time' do
    rb_full_time = subject.rb_full_time

    assert_nil rb_full_time.user
    assert_equal year, rb_full_time.year
    assert_equal month, rb_full_time.month
    assert_equal({ 3 => 8, 4 => 8, 5 => 8, 6 => 8, 7 => 8,
                   10 => 8, 11 => 8, 12 => 8, 13 => 8, 14 => 8,
                   17 => 8, 18 => 8, 19 => 8, 20 => 8, 21 => 8,
                   24 => 8, 25 => 8, 26 => 8, 27 => 8, 28 => 8,
                   31 => 8 }, rb_full_time.days)
    assert_equal 168, rb_full_time.total_hours
  end
end
