require 'test_helper'

class Api::V1::TemporaryAttachmentsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  test 'POST #create' do
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    post :create, params: { file: Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'),
                                                              'image/jpg') }
    assert_response :created, @response.body.to_s
    assert_equal 'jpg_705kB.jpg' , json_body['filename']
    assert_equal '/tmp/jpg_705kB', json_body['location'].split('.').first
  end
end
