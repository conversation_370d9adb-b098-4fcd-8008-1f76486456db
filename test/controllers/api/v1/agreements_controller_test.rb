require 'test_helper'

class Api::V1::AgreementsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :agreements

  def agreement
    return @agreement if @agreement.present?
    @agreement = agreements(:mkalita_agreement)
    Attachment.create(attachable: @agreement,
                      file: File.open(Rails.root.join('test', 'fixtures', 'files', 'jpg_705kB.jpg')))
    @agreement
  end

  def test_rescues_module_inclusion
    assert_includes Api::V1::AgreementsController.ancestors, Api::V1::Concerns::Handlers::Rescues
  end

  def test_index
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:agreements).size
    assert_equal 0, json_body.first['accepted_approvals_count']
  end

  def test_index_page_2
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    user_on_first_page = Agreement.order('id DESC').first!
    get :index, format: :json, params: { per_page: 1, page: 2 }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['id'].to_i > 0
    assert json_body.last['id'].to_i != user_on_first_page.id
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    get :index, format: :json, params: { f: { term: '3' } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, 1
    assert_equal json_body.first['name'], 'RODO 3'
  end

  def test_index_with_created_after
    agreement = agreements(:one)
    agreement.update_column(:created_at, 1.day.ago)
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s

    get :index, format: :json, params: { f: { created_after: Time.zone.today } }

    assert_response :success
    assert_equal Agreement.count - 1, json_body.count
  end

  def test_index_with_created_before
    agreement = agreements(:one)
    agreement.update_column(:created_at, 1.day.ago)
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s

    get :index, format: :json, params: { f: { created_before: Time.zone.yesterday } }

    assert_response :success
    assert_equal 1, json_body.count
  end

  def test_index_with_kind_filter
    agreement = agreements(:three)
    agreement.update_column(:kind, :training)
    authenticate(users(:wiktoria))

    get :index, format: :json, params: { f: { kind: 'training' } }

    assert_response :success
    assert_equal 1, json_body.count
  end

  def test_create
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    assert_difference('Agreement.count', 1) do
      post :create, format: :json, params: { agreement: {
        name: agreement.name + '_test_create',
        content: agreement.content + '_test_create',
        attachment_ids: [attachments(:one).id, attachments(:two).id],
        confirmation_button_text: agreement.confirmation_button_text,
        business_to_business: true,
        company_ids: [companies(:one).id],
        department_ids: [departments(:two).id]
      } }
      assert_response 201, @response.body.to_s
    end
    agreement = Agreement.find(json_body['id'])
    assert_equal agreement.name, json_body['name']
    assert_equal agreement.content, json_body['content']
    assert_equal agreement.attachments.count, 2
  end

  def test_create_without_files
    agreement = agreements(:three)
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    assert_difference('Agreement.count', 1) do
      post :create, format: :json, params: { agreement: {
        name: agreement.name + '_test_create',
        business_to_business: true,
        content: agreement.content + '_test_create',
        confirmation_button_text: agreement.confirmation_button_text,
        company_ids: [companies(:one).id],
        department_ids: [departments(:two).id]
      } }
      assert_response 201, @response.body.to_s
    end
    agreement = Agreement.find(json_body['id'])
    assert_equal agreement.name, json_body['name']
    assert_equal agreement.content, json_body['content']
    assert_equal agreement.attachments.count, 0
  end

  def test_faile_create
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    assert_difference('Agreement.count', 0) do
      post :create, params: { agreement: {
        name: '',
        content: '',
        attachment_ids: [attachments(:one), attachments(:two)],
        confirmation_button_text: ''
      } }
      assert_equal json_body['name'], ["can't be blank"]
      assert_equal json_body['content'], ["can't be blank"]
      assert_equal json_body['confirmation_button_text'], ["can't be blank"]
      assert_equal json_body['departments'], ["can't be blank"]
      assert_equal json_body['companies'], ["can't be blank"]
      assert_response 422, @response.body.to_s
    end
  end

  def test_show
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    get :show, params: { id: agreement }, format: :json
    assert_response :success, @response.body.to_s
    assert_equal agreement.name, json_body['name']
    assert_equal agreement.content, json_body['content']
  end

  def test_update
    agreement = agreements(:one)
    name = 'test_name'
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    put :update, params: { id: agreement, agreement: { name: name,
                                                       company_ids: [companies(:one).id],
                                                       department_ids: [departments(:two).id] } }
    assert_equal name, agreement.reload.name
  end

  def test_faile_update
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    put :update, params: { id: agreements(:one),
                           agreement: { name: '', attachments: [attachments(:one)],
                                        company_ids: [companies(:one).id],
                                        department_ids: [departments(:two).id] }  }
    refute_equal json_body['name'], ['']
    assert_response 422, @response.body.to_s
  end

  def test_destroy
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    assert_difference('Agreement.count', -1) do
      delete :destroy, format: :json, params: { id: agreement }
      assert_response 204, @response.body.to_s
    end
  end

  def test_form_data
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    get :form_data, format: :json
    assert_response :success
  end

  def test_index_authorization
    user = users(:mkalita_user)
    policy = stub(index?: false)
    AgreementPolicy.stubs(:new).with(user, Agreement).returns(policy)
    sign_in(user)
    get :index, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_show_authorization
    user = users(:mkalita_user)
    agreement = agreements(:mkalita_agreement)
    policy = stub(show?: false)
    AgreementPolicy.stubs(:new).with(user, agreement).returns(policy)
    sign_in(user)
    get :show, params: { id: agreement.id }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_create_authorization
    user = users(:mkalita_user)
    policy = stub(create?: false)
    AgreementPolicy.stubs(:new).with(user, Agreement).returns(policy)
    sign_in(user)
    post :create, params: { agreement: { name: 'Rola1', content: 'content' * 4 } }, format: :json
    assert_response 403, @response.body.to_s
  end
end
