# encoding: utf-8
require 'test_helper'

# debug sql:
# tail -n 20000 -f log/test.log | grep -A100 ": test_update_accept_nz_over_limit_succeeds_if_skip_warning_param_is_true"
class Api::V1::HolidayRequestsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :holiday_requests, :users, :memberships, :groups, :group_memberships, :roles, :membership_roles, :departments, :companies

  setup do
    Sidekiq::Testing.fake!
    Sidekiq::Worker.clear_all
    Rails.configuration.action_mailer.perform_deliveries = true
    ActionMailer::Base.deliveries.clear
    RequestStore.store[Userstamping.config[:store_key]] = users(:mkalita_user).to_global_id.to_s
  end

  teardown do
    Rails.configuration.action_mailer.perform_deliveries = false
    Sidekiq::Worker.clear_all
  end

  def holiday_request
    @holiday_request ||= holiday_requests(:one)
  end

  def test_history
    user = users(:mkalita_user)
    applicant = users(:milosz)
    authenticate(user)
    post :create, params: { holiday_request: {
      applicant_id: applicant.id, category: '<PERSON>ed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      starts_on: Date.today, ends_on: Date.today + 5.days
    } }, format: :json
    hr = HolidayRequest.find(json_body['id'])
    put :update, id: hr.id, holiday_request: { accept: true }, format: :json
    get :history, id: hr.id, format: :json
    assert_response :success
    assert_equal 2, json_body.count
    first_revision = json_body.first
    assert_equal(user.full_name, first_revision['author']['full_name'])
    refute_empty first_revision['changeset']
  end

  def test_filter_users_by_project_shows_nothing_to_common_user_not_related_by_department
    HolidayRequest.destroy_all

    viewer = users(:internal_user)
    viewer.groups.clear
    viewer.memberships.clear
    viewer.global_roles.clear
    viewer.global_roles << global_roles(:global_user)

    project = Project.create(
      name: 'BlaMyString',
      identifier: 'some-project-name',
      accounting_number: accounting_numbers(:one),
      description: 'Project description',
      company: viewer.company
    )
    assert project.persisted?, project.errors.inspect

    role = roles(:dev)
    group = groups(:mkalita_group)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    viewer.groups << group

    role = roles(:dev)
    applicant = users(:mkalita_user_alternative)
    um = applicant.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    um.roles << role
    um.reload

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-14', # past
      ends_on: '2000-01-30', # past
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!
    holiday_request.applicant.update(company_id: Company.native.first.id)

    @request.headers['X-Swagger-Sign-In-As'] = viewer.id.to_s
    get :users, format: :json, params: { f: { project_id: project.id.to_s } }
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    assert holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_filter_users_by_project_by_project_manager
    viewer = users(:internal_user)
    viewer.groups.clear
    viewer.memberships.clear
    viewer.global_roles.clear
    viewer.global_roles << global_roles(:global_pm)

    project = Project.create(
      name: 'BlaMyString',
      identifier: 'some-project-name',
      accounting_number: accounting_numbers(:one),
      description: 'Project description',
      company: viewer.company
    )
    assert project.persisted?, project.errors.inspect

    role = roles(:pm)
    group = groups(:mkalita_group)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    viewer.groups << group

    role = roles(:dev)
    applicant = users(:mkalita_user_alternative)
    um = applicant.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    um.roles << role
    um.reload

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-14', # past
      ends_on: '2000-01-30', # past
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!

    @request.headers['X-Swagger-Sign-In-As'] = viewer.id.to_s
    get :users, format: :json, params: { f: { project_id: project.id.to_s } }
    assert_equal User.active.native.where.not(company_id: nil)
                     .for_project(project.id).count, json_body.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_order_and_uniqueness
    HolidayRequest.destroy_all

    applicant = users(:mkalita_user_alternative)
    applicant.update_columns(department_id: departments(:mkalita_department).id)

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-14', # present
      ends_on: '2000-01-14', # present
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-26', # future
      ends_on: '2000-01-30',
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-11', # near past
      ends_on: '2000-01-13', # near past
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-16', # near future
      ends_on: '2000-01-20', # near future
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!

    applicant = users(:mkalita_global_admin_programmer)
    applicant.update_columns(department_id: departments(:mkalita_department).id)

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-12', # present
      ends_on: '2000-01-25',
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-26', # future
      ends_on: '2000-01-30',
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!

    applicant = users(:internal_user)
    applicant.update_columns(department_id: departments(:mkalita_department).id)

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-28', # far future
      ends_on: '2000-01-28',
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!

    viewer = users(:mkalita_user)
    viewer.global_roles << global_roles(:global_admin)
    applicant = viewer
    applicant.update_columns(department_id: departments(:mkalita_department).id)

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-01', # far past
      ends_on: '2000-01-10', # far past
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!

    applicant = users(:internal_user_alternative)
    applicant.update_columns(department_id: departments(:mkalita_department).id)

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-03', # far past
      ends_on: '2000-01-04',
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!
    holiday_request.applicant.update(company_id: Company.native.first.id)

    acceptable_order = %w(internal_user_alternative mgrabowski mromanow whanowerska)

    travel_to Time.zone.parse('2000-01-14') do
      @request.headers['X-Swagger-Sign-In-As'] = viewer.id.to_s
      # NOTE: users ignores `sort` param
      get :users, format: :json,
                  params: { f: { year_month: '2000-01',
                                 sort: 'ignore_me_please desc',
                                 x_holidays: true } }
      assert_response :success, @response.body.to_s
      refute json_body.empty?
      assert_equal acceptable_order, json_body.collect { |i| i['username'] }
      assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
    end
  end

  def test_users_x_holidays_header
    user = users(:mkalita_user)
    user.global_roles << global_roles(:global_admin)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { year_month: holiday_request.starts_on.to_date.strftime('%Y-%m'),
                               x_holidays: true }, x_holidays: true }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
    assert_equal "\"2016-08-06,2016-08-07,2016-08-13,2016-08-14,2016-08-15,2016-08-20,2016-08-21,2016-08-27,2016-08-28\"", response.header['X-Holidays']
  end

  def test_index_applicant_headers
    user = users(:mkalita_user)
    user.global_roles << global_roles(:global_admin)
    applicant = holiday_request.applicant
    applicant.absence_quota = 20
    applicant.absence_balance = 20
    applicant.save!
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json, params: { f: { applicant_id: holiday_request.applicant_id } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
    assert_match(%r{{\"absence_count_in_range\":\d+,\"absence_count_this_year\":\d+,\"absence_quota\":20,\"absence_balance\":20}}, response.header['X-Holidays-User-Statistics'])
  end

  def test_index_department_ids
    user = users(:wiktoria)
    department_id = departments(:two).id
    @request.headers['X-Swagger-Sign-In-As'] = nil
    authenticate(user)

    get :index, format: :json, params: { f: { departments_ids: [department_id] } }

    assert_response :success
    assert_equal HolidayRequest.joins(:applicant)
                               .where(users: { department_id: department_id })
                               .count,
                 json_body.count
  end

  def test_index_department_ids_my_subordinates
    user = users(:wiktoria)
    @request.headers['X-Swagger-Sign-In-As'] = nil
    authenticate(user)

    get :index, format: :json, params: { f: { departments_ids: ['my_subordinates'] } }

    assert_response :success
    assert_equal(
      HolidayRequest.joins(:applicant)
                    .where(users: { department_id: user.departments_as_chief_or_substitute_chief.pluck(:id) })
                    .count,
      json_body.count
    )
  end

  def test_index_department_ids_with_overuse_of_holidays
    user = users(:wiktoria)
    department_id = departments(:two).id
    authenticate(user)

    get :index, format: :json,
                params: { f: { department_ids: [department_id], overuse_of_holidays: true } }

    assert_response :success
    assert_equal 0, json_body.count
  end

  def test_index_not_confirmed
    user = users(:wiktoria)
    milosz_holiday = holiday_requests(:milosz_occassional_holiday)
    milosz_holiday.update(confirmed: true)
    authenticate(user)

    get :index, format: :json,
                params: { f: { accepted: true, not_confirmed: true } }

    assert_response :success
    assert json_body.all? { | holiday_request | assert_equal holiday_request['status'], "Accepted" }
    assert json_body.all? { | holiday_request | assert_equal holiday_request['confirmed'], false }
  end

  def test_index_pending_only
    user = users(:wiktoria)
    milosz_c_holiday = holiday_requests(:milosz_care_holiday)
    milosz_c_holiday.update(accepted_at: nil)
    milosz_holiday = holiday_requests(:milosz_occassional_holiday)
    milosz_holiday.update(confirmed: true)
    authenticate(user)

    get :index, format: :json,
                params: { f: { pending: true, accepted: false, not_confirmed: true } }

    assert_response :success
    assert json_body.all? { | holiday_request | assert_equal holiday_request['status'], "Pending" }
    assert json_body.all? { | holiday_request | assert_equal holiday_request['confirmed'], false }
  end

  def test_index_pending_and_accepted
    user = users(:wiktoria)
    milosz_c_holiday = holiday_requests(:milosz_care_holiday)
    milosz_c_holiday.update(accepted_at: nil)
    milosz_holiday = holiday_requests(:milosz_occassional_holiday)
    milosz_holiday.update(confirmed: true)
    authenticate(user)

    get :index, format: :json,
                params: { f: { pending: true, accepted: true } }

    assert_response :success
    assert_not json_body.all? { | holiday_request | holiday_request['status'] == "Pending" }
    assert_not json_body.all? { | holiday_request | holiday_request['confirmed'] == false }
  end

  def test_index_pending_accepted_and_not_confirmed
    user = users(:wiktoria)
    milosz_c_holiday = holiday_requests(:milosz_care_holiday)
    milosz_c_holiday.update(accepted_at: nil)
    milosz_holiday = holiday_requests(:milosz_occassional_holiday)
    milosz_holiday.update(confirmed: true)
    authenticate(user)

    get :index, format: :json,
                params: { f: { pending: true, accepted: true, not_confirmed: true } }

    assert_response :success
    assert_not json_body.all? { | holiday_request | holiday_request['status'] == "Pending" }
    assert json_body.all? { | holiday_request | holiday_request['confirmed'] == false }
  end

  def test_history
    user = users(:mkalita_user)
    applicant = users(:milosz)
    authenticate(user)
    post :create, params: { holiday_request: {
      applicant_id: applicant.id, category: 'Niedostępność',
      starts_on: Date.today, ends_on: Date.today + 5.days
    } }, format: :json
    hr = HolidayRequest.find(json_body['id'])
    put :update, id: hr.id, holiday_request: { accept: true }, format: :json
    get :history, id: hr.id, format: :json
    assert_response :success
    assert_equal 2, json_body.count

    first_revision = json_body.first
    assert_equal(user.full_name, first_revision['author']['full_name'])
    refute_empty first_revision['changeset']
  end

  def test_history
    user = users(:mkalita_user)
    applicant = users(:wiktoria)
    HolidayRequest.where(applicant_id: applicant.id).destroy_all
    authenticate(user)
    post :create, params: { holiday_request: {
      applicant_id: applicant.id, category: 'Niedostępność',
      starts_on: Date.today, ends_on: Date.today + 5.days
    } }, format: :json
    hr = HolidayRequest.find(json_body['id'])
    put :update, params: { id: hr.id, holiday_request: { accept: true, skip_warning: true } },
                 format: :json
    get :history, params: { id: hr.id }, format: :json
    assert_response :success
    assert_equal 2, json_body.count
    first_revision = json_body.first
    assert_equal(user.full_name, first_revision['author']['full_name'])
    refute_empty first_revision['changeset']
  end

  def test_category_options
    @request.headers['X-Swagger-Sign-In-As'] = holiday_request.applicant_id.to_s
    get :category_options, format: :json
    assert_response :success, @response.body.to_s
    assert_equal HolidayRequest::CURRENTLY_SELECTABLE_CATEGORIES.size, json_body.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_category_options_for_common_user
    user = users(:mkalita_user_1)
    user.global_roles << global_roles(:global_user)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :category_options, format: :json
    assert_response :success, @response.body.to_s
    assert_not_empty json_body
    assert_includes json_body, 'Niedostępność'
    assert_not_includes json_body, 'Niedostępność/Ż'
    assert_not_includes json_body, 'Niedostępność/DW'
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_category_options_for_potential_examiner_user
    user = users(:mkalita_user)
    user.global_roles << global_roles(:global_admin)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :category_options, format: :json
    assert_response :success, @response.body.to_s
    assert_not_empty json_body
    assert_includes json_body, 'Niedostępność'
    assert_includes json_body, 'Niedostępność/Ż'
    assert_includes json_body, 'Niedostępność/DW'
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_project_options_not_empty_for_global_pm
    viewer = users(:internal_user)
    viewer.global_roles.clear
    viewer.global_roles << global_roles(:global_pm)
    viewer.memberships.destroy_all

    enough_fixtures = []
    Project.where(status: Project.statuses[:active]).each do |project|
      users = User.active.for_project(project.id)
      enough_fixtures << users.first
    end
    refute_empty enough_fixtures.compact

    @request.headers['X-Swagger-Sign-In-As'] = viewer.id.to_s
    get :project_options, format: :json
    refute_empty json_body
    proj_ids = []
    json_body.each do |proj|
      proj_ids << proj['id'].to_i
    end
    assert_equal Project.where(status: Project.statuses[:active]).pluck(:id).sort, proj_ids.sort
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_project_options_empty_for_common_user_non_member
    viewer = users(:internal_user)
    viewer.global_roles.clear
    viewer.global_roles << global_roles(:global_user)
    viewer.memberships.destroy_all

    enough_fixtures = []
    Project.where(status: Project.statuses[:active]).each do |project|
      users = User.active.for_project(project.id)
      enough_fixtures << users.first
    end
    refute_empty enough_fixtures.compact

    @request.headers['X-Swagger-Sign-In-As'] = viewer.id.to_s
    get :project_options, format: :json
    assert_empty json_body
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_project_options_for_member_via_user_for_common_user
    viewer = users(:internal_user)
    viewer.global_roles.clear
    viewer.global_roles << global_roles(:global_user)
    viewer.memberships.destroy_all

    project = projects(:mkalita_project)
    assert project.active?
    role = roles(:pm)
    um = viewer.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    um.roles << role
    um.reload

    @request.headers['X-Swagger-Sign-In-As'] = viewer.id.to_s
    get :project_options, format: :json
    refute_empty json_body
    json_body.each do |proj|
      assert_equal proj['id'].to_i, project.id
    end
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_project_options_for_member_via_group_for_common_user
    viewer = users(:internal_user)
    viewer.global_roles.clear
    viewer.global_roles << global_roles(:global_user)
    viewer.memberships.destroy_all

    project = projects(:mkalita_project)
    assert project.active?
    role = roles(:pm)
    group = groups(:mkalita_group)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    viewer.groups << group

    @request.headers['X-Swagger-Sign-In-As'] = viewer.id.to_s
    get :project_options, format: :json
    refute_empty json_body
    json_body.each do |proj|
      assert_equal proj['id'].to_i, project.id
    end
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_department_options
    unassigned_departments_count = Department.where(company_id: nil).count
    user = users(:mkalita_user_1)
    user.update_columns(company_id: companies(:mkalita_company).id)
    user.update_column(:department_id, departments(:mkalita_department).id)
    user.global_roles << global_roles(:global_user)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :department_options, format: :json
    assert_response :success, @response.body.to_s
    assert_equal Department.not_locked.count + unassigned_departments_count, json_body.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_department_options_for_separete_company
    unassigned_departments_count = Department.where(company_id: nil).count
    user = users(:mkalita_user_1)
    user.update_columns(company_id: companies(:filmweb).id)
    user.update_column(:department_id, departments(:mkalita_department).id)
    user.global_roles << global_roles(:global_user)
    as_chief_count = Department.where('chief_id = :current_user_id', current_user_id: user.id).count
    as_substitute_chief_count = Department.where('substitute_chief_id = :current_user_id', current_user_id: user.id).count
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :department_options, format: :json
    assert_response :success, @response.body.to_s
    assert_equal 0, json_body.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_department_options_for_common_user
    unassigned_departments_count = Department.where(company_id: nil).count
    user = users(:mkalita_user_1)
    user.update_columns(company_id: companies(:mkalita_company).id)
    user.global_roles << global_roles(:global_user)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    refute Department.where('chief_id = :current_user_id OR substitute_chief_id = :current_user_id', current_user_id: user.id).exists?
    get :department_options, format: :json
    assert_response :success, @response.body.to_s
    assert_equal Department.not_locked.count + unassigned_departments_count, json_body.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_department_options_for_global_admin
    user = users(:mkalita_user_1)
    user.departments.delete_all
    user.global_roles << global_roles(:global_admin)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    refute Department.where('chief_id = :current_user_id OR substitute_chief_id = :current_user_id', current_user_id: user.id).exists?
    get :department_options, format: :json
    assert_response :success, @response.body.to_s
    assert_equal assigns(:departments).size, json_body.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_user_options_for_global_admin
    users_count = User.native.count
    user = users(:mkalita_user_1)
    user.departments.delete_all
    assert_equal 0, user.departments.count
    user.global_roles << global_roles(:global_admin)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    refute Department.where('chief_id = :current_user_id OR substitute_chief_id = :current_user_id', current_user_id: user.id).exists?
    get :user_options, format: :json
    assert_response :success, @response.body.to_s
    assert_equal users_count, json_body.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_user_options_with_term
    user = users(:wiktoria)
    authenticate(user)
    searched_for_user = users(:milosz)

    get :user_options, params: { f: { term: searched_for_user.full_name } }, format: :json

    assert_response :success
    assert_equal 1, json_body.size
  end

  def test_user_options_for_unprivileged_user
    user = users(:milosz)
    @request.headers['X-Swagger-Sign-In-As'] = nil
    authenticate(user)
    department = user.department

    get :user_options, format: :json

    assert_response :success
    assert_equal department.users.count, json_body.count
  end

  def test_rescues_module_inclusion
    assert_includes Api::V1::HolidayRequestsController.ancestors, Api::V1::Concerns::Handlers::Rescues
  end

  ### users

  def test_users_not_empty_scope_for_separate_company
    user = User.internal.last
    user.update_columns(company_id: companies(:filmweb).id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    Department.where(chief_id: user.id).destroy_all
    Department.where(substitute_chief_id: user.id).destroy_all
    user.applicant_holiday_requests.destroy_all
    holiday_request = HolidayRequest.new(
      applicant_id: user.id,
      starts_on: '2040-01-14',
      ends_on: '2040-01-30',
      created_by_user_id: user.id,
      updated_by_user_id: user.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!
    holiday_request.applicant.update(company_id: Company.native.first.id)
    viewer = User.internal.first
    viewer.global_roles.clear
    viewer.global_roles << global_roles(:global_user)
    assert user != viewer
    viewer.update_columns(company_id: companies(:filmweb).id, password_changed_at: Time.now)
    @request.headers['X-Swagger-Sign-In-As'] = viewer.id.to_s
    get :users, format: :json
    assert_response :success, @response.body.to_s
    assert_equal user.applicant_holiday_requests.count, json_body.second['holiday_requests'].size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_includes_actions_for_current_user_for_chiefy
    user = User.internal.last
    user.applicant_holiday_requests.destroy_all
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    user.reload
    assert user.global_roles.count == 1 && user.global_roles.first.name == 'Global User'
    department = Department.create!(name: 'xyz', chief_id: user.id, mpk_number: mpk_numbers(:other))
    some_user = User.internal.first
    department.users << some_user
    assert_equal 1, department.users.count
    some_user.applicant_holiday_requests.create!(
      starts_on: '2030-01-01',
      ends_on: '2030-01-02',
      created_by_user_id: some_user.id,
      updated_by_user_id: some_user.id,
      modified_by_user_at: Time.zone.now
    )
    assert_equal 1, some_user.applicant_holiday_requests.count
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, params: { f: { pending: true } }, format: :json
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    json_body.each do |usr|
      usr['holiday_requests'].each do |element|
        is_creator = element['created_by_user_id'].to_s == user.id.to_s
        is_applicant = element['applicant_id'].to_s == user.id.to_s
        if is_creator || is_applicant
          refute element['actions_for_current_user'].empty?
          assert element['actions_for_current_user']['update']
          if is_creator
            assert element['actions_for_current_user']['destroy']
          end
        else
          refute element['actions_for_current_user'].empty?
          assert element['actions_for_current_user']['update']
          refute element['actions_for_current_user']['destroy'] # not admin
        end
      end
    end
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_includes_actions_for_current_user_for_applicant
    user = holiday_request.applicant
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    user.reload
    assert user.global_roles.count == 1 && user.global_roles.first.name == 'Global User'
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json
    assert_response :success, @response.body.to_s
    assert_not json_body.empty?
    holiday_requests = json_body.detect { |u| u['username'] == 'mgrabowski' }['holiday_requests']

    assert_not holiday_requests.first['actions_for_current_user'].empty?
    assert_not holiday_requests.first['actions_for_current_user']['accept']
    assert_not holiday_requests.first['actions_for_current_user']['update']
    json_body.each do |usr|
      usr['holiday_requests'].each do |element|
        if element['created_by_user_id'].to_s == user.id.to_s || element['applicant_id'].to_s == user.id.to_s
          assert element['actions_for_current_user']['destroy']
        else
          assert_not element['actions_for_current_user']['destroy']
        end
      end
    end
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_includes_actions_for_current_user_for_applicant_who_is_board_member
    user = holiday_request.applicant
    user.update_columns(department_id: departments(:board_member_department).id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    user.update(company_id: Company.native.first.id)
    user.reload
    assert user.global_roles.count == 1 && user.global_roles.first.name == 'Global User'
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, params: { f: { applicant_id: user.id } }, format: :json
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    refute json_body.sort_by! { |el| el[:id] }.third['holiday_requests'].empty?
    refute json_body.sort_by! { |el| el[:id] }.third['holiday_requests'].last['actions_for_current_user'].empty?
    assert json_body.sort_by! { |el| el[:id] }.third['holiday_requests'].last['actions_for_current_user']['update']
    assert json_body.sort_by! { |el| el[:id] }.third['holiday_requests'].last['actions_for_current_user']['accept'] # board_member
    json_body.each do |usr|
      usr['holiday_requests'].each do |element|
        assert element['actions_for_current_user']['destroy']
      end
    end
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_includes_actions_for_current_user_for_admin
    assert HolidayRequest.count > 1
    user = User.where.not(company_id: nil).last
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    assert user.global_roles.count == 1 && user.global_roles.first.name == 'Global System Administrator'
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    refute json_body.first['holiday_requests'].last['actions_for_current_user'].empty?
    json_body.each do |usr|
      usr['holiday_requests'].each do |element|
        assert element['actions_for_current_user']['update']
        assert element['actions_for_current_user']['accept']
        assert element['actions_for_current_user']['destroy']
      end
    end
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users
    holiday_request.applicant.update(company_id: Company.native.first.id)
    @request.headers['X-Swagger-Sign-In-As'] = holiday_request.applicant_id.to_s
    get :users, format: :json
    assert_response :success, @response.body.to_s
    assert_equal User.native.active.where.not(company_id: nil).count, json_body.size
    assert_equal holiday_request.applicant.applicant_holiday_requests.count,
                 json_body.detect { |u| u['username'] == holiday_request.applicant.username }[
                   'holiday_requests'
                 ].size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_page_2
    assert HolidayRequest.count > 1
    assert_equal 3, User.includes(:applicant_holiday_requests).references(:holiday_requests).where.not(holiday_requests: { id: nil }).count
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json, params: { per_page: 1, page: 2 }
    assert_response :success, @response.body.to_s
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_rejects_invalid
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: '2017-01-25', ends_on_before: '2017-01-23' } }
    assert_response :bad_request, @response.body.to_s
    assert_equal({'base' => ['invalid date range']}, json_body['errors'])
    assert_equal 'application/json; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_mismatch_lower
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: '2017-01-22', ends_on_before: '2017-01-22' } }
    assert_response :success, @response.body.to_s
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    refute holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_mismatch_upper
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: '2017-01-26', ends_on_before: '2017-01-26' } }
    assert_response :success, @response.body.to_s
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    refute holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_match
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: starts, ends_on_before: ends } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    assert holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_start_overlaps
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: '2017-01-22', ends_on_before: ends } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal 1, json_body.map { |usr|
      usr['holiday_requests']
    }.flatten.uniq.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_for_specific_year
    get :users, format: :json, params: { f: { year: 2017 } }

    assert_response :success
    assert_equal 3, json_body.count
  end

  def test_users_search_date_range_end_overlaps
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: starts, ends_on_before: '2017-01-26' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    assert holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_overlaps
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: '2017-01-22', ends_on_before: '2017-01-26' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    assert_equal 1, holiday_requests.size
    assert holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_start_intersects
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: '2017-01-24', ends_on_before: '2017-01-25' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    assert holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_start_intersects_only_with_end
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: '2017-01-25', ends_on_before: '2017-01-26' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    assert holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_start_intersects_only
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: '2017-01-24', ends_on_before: '2017-01-26' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    assert holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_end_intersects_only_with_start
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: '2017-01-22', ends_on_before: '2017-01-23' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    assert holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_end_intersects_only
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: '2017-01-22', ends_on_before: '2017-01-24' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    assert holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_search_date_range_intersects
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.update(company_id: Company.native.first.id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :users, format: :json,
                params: { f: { starts_on_after: '2017-01-24', ends_on_before: '2017-01-24' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    holiday_requests = json_body.map { |usr| usr['holiday_requests'] }.flatten
    assert holiday_requests.detect { |hr| hr['id'] == holiday_request.id }
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_users_sick_absence_balance_change
    wiktoria = users(:wiktoria)

    get :users, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.find { |user| user["id"] == wiktoria.id }["sick_absence_balance"], 10

    HolidayRequest.create!(
      applicant: wiktoria,
      created_by_user: wiktoria,
      updated_by_user: wiktoria,
      modified_by_user_at: Time.now,
      examiner: wiktoria,
      category: 1,
      visible: true,
      starts_on: Date.new(2025, 3, 19),
      ends_on: Date.new(2025, 3, 19),
      accepted_at: Date.new(2025, 3, 19)
    )

    get :users, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.find { |user| user["id"] == wiktoria.id }["sick_absence_balance"], 9
  end

  def test_users_has_active_employment_contact_change
    wiktoria = users(:wiktoria)

    get :users, format: :json
    assert_response :success, @response.body.to_s
    assert_not json_body.find { |user| user["id"] == wiktoria.id }["has_active_employment_contact"]

    UserContract.create!(
      {
        user: wiktoria,
        agreement_type: 'employment',
        starts_on: '2016-01-01',
        ends_on: Date.current + 6.week,
        month_notice_period: 1,
      })

    get :users, format: :json
    assert_response :success, @response.body.to_s
    assert json_body.find { |user| user["id"] == wiktoria.id }["has_active_employment_contact"]
  end

  def test_users_has_active_employment_contact_not_change
    wiktoria = users(:wiktoria)

    get :users, format: :json
    assert_response :success, @response.body.to_s
    assert_not json_body.find { |user| user["id"] == wiktoria.id }["has_active_employment_contact"]

    UserContract.create!(
      {
        user: wiktoria,
        agreement_type: 'employment',
        starts_on: '2016-01-01',
        ends_on: Date.current - 6.week,
        month_notice_period: 1,
      })

    get :users, format: :json
    assert_response :success, @response.body.to_s
    assert_not json_body.find { |user| user["id"] == wiktoria.id }["has_active_employment_contact"]
  end

  ### index

  def test_index_with_param_overuse_of_holidays_unauthorized
    HolidayRequest.destroy_all
    user = users(:internal_user)
    creator = users(:mkalita_user_alternative)

    holiday_request = HolidayRequest.create!(
      applicant_id: user.id,
      starts_on: '2040-02-06',
      ends_on: '2040-02-10',
      category: 'Niedostępność/Ż',
      created_by_user_id: creator.id,
      updated_by_user_id: creator.id,
      modified_by_user_at: Time.zone.now,
      accepted_at: Time.zone.now,
      rejected_at: nil
    )
    assert_equal 5, holiday_request.business_days.size

    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { year_month: '2040-02', overuse_of_holidays: true } }
    assert_response :forbidden, @response.body.to_s
    assert_equal({'base' => ['not allowed to use filter: overuse_of_holidays']}, json_body['errors'])
    assert_equal 'application/json; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_param_sort_by_starts_on_asc
    assert HolidayRequest.count > 1
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    first_record = HolidayRequest.order('starts_on ASC').first!
    get :index, format: :json, params: { f: { sort: 'starts_on asc' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal first_record.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_param_sort_by_starts_on_desc
    assert HolidayRequest.count > 1
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    first_record = HolidayRequest.order('starts_on DESC').first!
    get :index, params: { f: { sort: 'starts_on desc' } }, format: :json
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal first_record.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_param_collection_for_select
    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:holiday_requests).size
    assert !json_body.include?('created_at')
    assert !json_body.include?('category')
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_empty_past_scope_for_global_user
    HolidayRequest.destroy_all

    viewer = users(:internal_user)
    viewer.global_roles.clear
    viewer.global_roles << global_roles(:global_user)

    applicant = users(:mkalita_user_alternative)

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-14', # past
      ends_on: '2000-01-30', # past
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!

    @request.headers['X-Swagger-Sign-In-As'] = viewer.id.to_s
    get :index, format: :json,
                params: { f: { year_month: '2000-01', pending: true, visible: false } }
    assert_response :success, @response.body.to_s
    assert json_body.empty?
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_non_empty_past_scope_for_project_manager
    HolidayRequest.destroy_all

    viewer = users(:internal_user)
    viewer.global_roles.clear
    viewer.global_roles << global_roles(:global_pm)

    applicant = users(:mkalita_user_alternative)

    holiday_request = HolidayRequest.new(
      applicant_id: applicant.id,
      starts_on: '2000-01-14', # past
      ends_on: '2000-01-30', # past
      created_by_user_id: applicant.id,
      updated_by_user_id: applicant.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!

    @request.headers['X-Swagger-Sign-In-As'] = viewer.id.to_s
    get :index, format: :json,
                params: { f: { year_month: '2000-01', pending: true, visible: false } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal holiday_request.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_term_filter
    user = users(:wiktoria)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json, params: { f: { term: 'hanowerska' } }

    assert_response :success
    assert_equal 1, json_body.count
  end

  def test_index_with_created_before_filter
    user = users(:wiktoria)
    authenticate(user)
    holiday_request = holiday_requests(:wiktoria_unpaid_holiday)
    holiday_request.update_column(:created_at, 2.days.ago)

    get :index, format: :json, params: { f: { created_before: Time.zone.yesterday } }
    assert_response :success
    assert_equal 1, json_body.count
    assert_equal holiday_request.id, json_body.first['id']
  end

  def test_index_with_created_after_filter
    user = users(:wiktoria)
    authenticate(user)
    holiday_request = holiday_requests(:wiktoria_unpaid_holiday)
    holiday_request.update_column(:created_at, 2.days.from_now)

    get :index, format: :json, params: { f: { created_after: Time.zone.tomorrow } }
    assert_response :success
    assert_equal 1, json_body.count
    assert_equal holiday_request.id, json_body.first['id']
  end

  def test_index_with_category_filter
    user = users(:wiktoria)
    authenticate(user)

    get :index, format: :json, params: { f: { category: HolidayRequest::ABSENCE_UNPAID } }

    assert_response :success
    assert json_body.all? do |holiday_request|
      holiday_request['category'] == HolidayRequest::ABSENCE_UNPAID
    end
  end

  def test_index_empty_scope
    user = User.internal.last
    Department.where(chief_id: user.id).destroy_all
    Department.where(substitute_chief_id: user.id).destroy_all
    user.applicant_holiday_requests.destroy_all
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    holiday_requests(:milosz_holiday).destroy
    HolidayRequest.destroy_all
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert json_body.empty?
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_not_empty_scope_for_separate_company
    user = User.internal.last
    user.update_columns(company_id: companies(:filmweb).id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    Department.where(chief_id: user.id).destroy_all
    Department.where(substitute_chief_id: user.id).destroy_all
    user.applicant_holiday_requests.destroy_all
    holiday_request = HolidayRequest.new(
      applicant_id: user.id,
      starts_on: '2040-01-14',
      ends_on: '2040-01-30',
      created_by_user_id: user.id,
      updated_by_user_id: user.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!
    viewer = User.internal.first
    viewer.global_roles.clear
    viewer.global_roles << global_roles(:global_user)
    assert user != viewer
    viewer.update_columns(company_id: companies(:filmweb).id, password_changed_at: Time.now)
    @request.headers['X-Swagger-Sign-In-As'] = viewer.id.to_s
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal 1, json_body.size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_includes_actions_for_current_user_for_chiefy
    user = User.internal.last
    user.applicant_holiday_requests.destroy_all
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    user.reload
    assert user.global_roles.count == 1 && user.global_roles.first.name == 'Global User'
    department = Department.create!(name: 'xyz', chief_id: user.id, mpk_number: mpk_numbers(:other))
    some_user = User.internal.first
    department.users << some_user
    assert_equal 1, department.users.count
    some_user.applicant_holiday_requests.create!(
      starts_on: '2030-01-01',
      ends_on: '2030-01-02',
      created_by_user_id: some_user.id,
      updated_by_user_id: some_user.id,
      modified_by_user_at: Time.zone.now
    )
    assert_equal 1, some_user.applicant_holiday_requests.count
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json, params: { f: { pending: true } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    json_body.each do |element|
      is_creator = element['created_by_user_id'].to_s == user.id.to_s
      is_applicant = element['applicant_id'].to_s == user.id.to_s
      if is_creator || is_applicant
        refute element['actions_for_current_user'].empty?
        assert element['actions_for_current_user']['update']
        if is_creator
          assert element['actions_for_current_user']['destroy']
        end
      else
        refute element['actions_for_current_user'].empty?
        assert element['actions_for_current_user']['update']
        refute element['actions_for_current_user']['destroy'] # not admin
      end
    end
    assert json_body.last['actions_for_current_user']['accept']
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_includes_actions_for_current_user_for_applicant
    user = holiday_request.applicant
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    user.reload
    assert user.global_roles.count == 1 && user.global_roles.first.name == 'Global User'
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    refute json_body.last['actions_for_current_user'].empty?
    assert json_body.last['actions_for_current_user']['update']
    json_body.each do |element|
      if element['created_by_user_id'].to_s == user.id.to_s || element['applicant_id'].to_s == user.id.to_s
        assert element['actions_for_current_user']['destroy']
      else
        refute element['actions_for_current_user']['destroy']
      end
    end
    refute json_body.last['actions_for_current_user']['accept']
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_includes_actions_for_current_user_for_applicant_who_is_board_member
    user = holiday_request.applicant
    user.update_columns(department_id: departments(:board_member_department).id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    user.reload
    assert user.global_roles.count == 1 && user.global_roles.first.name == 'Global User'
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, params: { f: { applicant_id: user.id } }, format: :json
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    refute json_body.last['actions_for_current_user'].empty?
    assert json_body.last['actions_for_current_user']['update']
    json_body.each do |element|
      assert element['actions_for_current_user']['destroy']
    end
    assert json_body.last['actions_for_current_user']['accept'] # board_member
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_includes_actions_for_current_user_for_admin
    assert HolidayRequest.count > 1
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    assert user.global_roles.count == 1 && user.global_roles.first.name == 'Global System Administrator'
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    refute json_body.last['actions_for_current_user'].empty?
    assert json_body.last['actions_for_current_user']['update']
    json_body.each do |element|
      assert element['actions_for_current_user']['destroy']
    end
    assert json_body.last['actions_for_current_user']['accept']
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index
    @request.headers['X-Swagger-Sign-In-As'] = holiday_request.applicant_id.to_s
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:holiday_requests).size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_page_2
    assert HolidayRequest.count > 1
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    record_on_first_page = HolidayRequest.order('id DESC').first!
    get :index, format: :json, params: { per_page: 1, page: 2 }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['id'].to_i > 0
    refute_equal record_on_first_page.id, json_body.last['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_rejects_invalid
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: '2017-01-25', ends_on_before: '2017-01-23' } }
    assert_response :bad_request, @response.body.to_s
    assert_equal({'base' => ['invalid date range']}, json_body['errors'])
    assert_equal 'application/json; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_mismatch_lower
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: '2017-01-22', ends_on_before: '2017-01-22' } }
    assert_response :success, @response.body.to_s
    if json_body.empty?
      assert true
    else
      refute_equal holiday_request.id, json_body.first['id'].to_i
    end
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_mismatch_upper
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: '2017-01-26', ends_on_before: '2017-01-26' } }
    assert_response :success, @response.body.to_s
    if json_body.empty?
      assert true
    else
      refute_equal holiday_request.id, json_body.first['id'].to_i
    end
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_match
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json, params: { f: { starts_on_after: starts, ends_on_before: ends } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal holiday_request.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_start_overlaps
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: '2017-01-22', ends_on_before: ends } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal holiday_request.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_end_overlaps
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: starts, ends_on_before: '2017-01-26' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal holiday_request.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_overlaps
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: '2017-01-22', ends_on_before: '2017-01-26' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal holiday_request.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_start_intersects
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: '2017-01-24', ends_on_before: '2017-01-25' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal holiday_request.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_start_intersects_only_with_end
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: '2017-01-25', ends_on_before: '2017-01-26' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal holiday_request.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_with_starts_on_after
    user = users(:wiktoria)
    authenticate(user)

    get :index,
        format: :json,
        params: { f: { starts_on_after: Time.zone.today.beginning_of_month + 19.days } }

    assert_response :success
    assert_not_empty json_body
    assert_equal holiday_requests(:milosz_educational_holiday).id, json_body.first['id']
  end

  def test_index_search_date_range_start_intersects_only
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: '2017-01-24', ends_on_before: '2017-01-26' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal holiday_request.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_end_intersects_only_with_start
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: '2017-01-22', ends_on_before: '2017-01-23' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal holiday_request.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_end_intersects_only
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: '2017-01-22', ends_on_before: '2017-01-24' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal holiday_request.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_search_date_range_intersects
    holiday_request = holiday_requests(:many_days)
    starts = '2017-01-23'
    ends = '2017-01-25'
    assert_equal starts, holiday_request.starts_on.strftime('%Y-%m-%d')
    assert_equal ends, holiday_request.ends_on.strftime('%Y-%m-%d')
    user = User.where.not(company_id: nil).last
    user.global_roles.clear
    user.global_roles << global_roles(:global_admin)
    user.reload
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    get :index, format: :json,
                params: { f: { starts_on_after: '2017-01-24', ends_on_before: '2017-01-24' } }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert_equal holiday_request.id, json_body.first['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  ###

  def test_create
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    assert_difference('HolidayRequest.count') do
      post :create, params: { holiday_request: {
        accepted_at: holiday_request.accepted_at,
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: holiday_request.applicant_id,
        category: holiday_request.category,
        ends_on: Time.zone.parse('2014-01-02').to_date,
        examiner_comment: holiday_request.examiner_comment,
        examiner_id: holiday_request.examiner_id,
        rejected_at: holiday_request.rejected_at,
        starts_on: Time.zone.parse('2014-01-02').to_date,
        visible: holiday_request.visible
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    assert_equal holiday_request_url(HolidayRequest.find(json_body['id'])), response.location
  end

  def test_create_with_bad_category_as_common_user
    user = users(:mkalita_user_1)
    user.company = companies(:one)
    user.global_roles << global_roles(:global_user)
    user.save
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

    post :create, params: { holiday_request: {
      applicant_comment: holiday_request.applicant_comment,
      category: 'Niedostępność/Ż',
      applicant_id: user.id,
      ends_on: 1.week.from_now.to_date,
      starts_on: Time.zone.today
    } }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['category']
  end

  def test_create_populates_subcategory
    user = users(:milosz)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    HolidayRequest.destroy_all

    assert_difference('HolidayRequest.count') do
      post :create, params: { holiday_request: {
        category: 'Niedostępność/O',
        subcategory: 'parental',
        applicant_id: user.id,
        starts_on: (Time.zone.today + 2.weeks).beginning_of_week,
        ends_on: Time.zone.today + 3.weeks
      } }, format: :json
    end

    holiday_request = HolidayRequest.find(json_body['id'])
    assert holiday_request.parental?
  end

  def test_create_populates_hours
    user = users(:milosz)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    HolidayRequest.destroy_all

    assert_difference('HolidayRequest.count') do
      post :create, params: { holiday_request: {
        category: 'Niedostępność/O',
        subcategory: 'parental',
        applicant_id: user.id,
        starts_on: (Time.zone.today + 2.weeks).beginning_of_week,
        ends_on: Time.zone.today + 3.weeks,
        hours: 4
      } }, format: :json
    end

    holiday_request = HolidayRequest.find(json_body['id'])
    assert_equal 4, holiday_request.hours
  end

  def test_create_free_day_request
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    assert_difference('HolidayRequest.count') do
      post :create, params: { holiday_request: {
        accepted_at: holiday_request.accepted_at,
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: holiday_request.applicant_id,
        category: HolidayRequest::ABSENCE_FREE_DAY,
        ends_on: Time.zone.parse('2014-01-02').to_date,
        examiner_comment: holiday_request.examiner_comment,
        examiner_id: holiday_request.examiner_id,
        rejected_at: holiday_request.rejected_at,
        starts_on: Time.zone.parse('2014-01-02').to_date,
        visible: holiday_request.visible
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    assert_equal holiday_request_url(HolidayRequest.find(json_body['id'])), response.location
  end

  def test_email_on_create_is_skipped_if_submitted_by_common_user_applicant
    @request.headers['X-Swagger-Sign-In-As'] = users(:internal_user).id.to_s
    applicant_id = users(:internal_user).id
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność',
        ends_on: Time.zone.parse('2014-01-02').to_date,
        starts_on: Time.zone.parse('2014-01-02').to_date,
        visible: holiday_request.visible
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_equal json_body['applicant_id'], applicant_id
      assert_nil json_body['examiner_id']
      holiday_request = HolidayRequest.find(json_body['id'])
      assert_empty ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_email_on_create_is_delivered_with_expected_content_if_submitted_for_common_user_applicant_by_an_entitled_user
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    applicant_id = users(:internal_user).id
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność',
        ends_on: Time.zone.parse('2014-01-02').to_date,
        starts_on: Time.zone.parse('2014-01-02').to_date,
        visible: holiday_request.visible
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_equal json_body['applicant_id'], applicant_id
      assert_nil json_body['examiner_id']
      holiday_request = HolidayRequest.find(json_body['id'])
      emails = [
        holiday_request.applicant.email
      ]
      assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_email_on_create_is_delivered_with_expected_content_when_department_chief_is_applicant
    department = Department.new(
      name: 'department_test',
      chief_id: users(:mkalita_user_alternative).id,
      mpk_number: mpk_numbers(:other),
      uber_chief_id: users(:internal_user).id
    )
    assert department.save, department.errors.inspect
    applicant_id = department.chief.id
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: holiday_request.category,
        ends_on: Time.zone.parse('2014-01-02').to_date,
        starts_on: Time.zone.parse('2014-01-02').to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_equal json_body['applicant_id'], applicant_id
      holiday_request = HolidayRequest.find(json_body['id'])
      emails = [
        holiday_request.applicant.email,
        department.uber_chief.email
      ]
      assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_email_on_create_is_delivered_with_expected_content_when_department_chief_is_creator
    chief = users(:mkalita_user)
    department = Department.new(
      name: 'department_test',
      chief_id: chief.id,
      mpk_number: mpk_numbers(:other)
    )
    assert department.save, department.errors.inspect
    applicant = users(:mkalita_user_alternative)
    applicant_id = applicant.id
    applicant.department = department
    applicant.save
    assert applicant.departments_as_chief.empty?
    assert applicant.reload.departments.include?(department)
    @request.headers['X-Swagger-Sign-In-As'] = chief.id.to_s
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: holiday_request.category,
        ends_on: Time.zone.parse('2014-01-02').to_date,
        starts_on: Time.zone.parse('2014-01-02').to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_equal json_body['applicant_id'], applicant_id
      holiday_request = HolidayRequest.find(json_body['id'])
      assert_equal chief.id, holiday_request.updated_by_user_id
      emails = [
        holiday_request.applicant.email
      ]
      assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_cant_spoof_examiner_id
    applicant = users(:internal_user)
    applicant_id = applicant.id
    examiner_id = users(:mkalita_user).id
    @request.headers['X-Swagger-Sign-In-As'] = applicant_id.to_s
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność',
        ends_on: Time.zone.parse('2014-01-02').to_date,
        examiner_comment: 'test',
        examiner_id: examiner_id,
        starts_on: Time.zone.parse('2014-01-02').to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_nil json_body['examiner_id']
    end
  end

  def test_examiner_id_is_not_set_when_created_and_autoaccepted
    internal_user = users(:internal_user)
    internal_user.update_columns(department_id: departments(:board_member_department).id)
    applicant = users(:mkalita_user_alternative)
    applicant.update_columns(department_id: departments(:board_member_department).id)
    applicant_id = applicant.id
    creator_id = users(:mkalita_user).id
    @request.headers['X-Swagger-Sign-In-As'] = creator_id.to_s
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność/Ch',
        ends_on: Time.zone.parse('2014-01-02').to_date,
        examiner_comment: 'test',
        starts_on: Time.zone.parse('2014-01-02').to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_nil json_body['examiner_id']
    end
  end

  def test_cant_spoof_examiner_comment_if_current_user_is_plain_user
    Department.update_all(board_member: false)
    applicant =  users(:internal_user)
    applicant_id = applicant.id
    @request.headers['X-Swagger-Sign-In-As'] = applicant_id.to_s
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność/O',
        ends_on: Time.zone.parse('2014-01-02').to_date,
        examiner_comment: 'test',
        starts_on: Time.zone.parse('2014-01-02').to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_nil json_body['accepted_at']
    end
  end

  def test_O_holiday_request_for_user_with_employment_contract_should_be_accepted
    travel_to(Date.new(2025, 2, 25))

    Department.update_all(board_member: false)
    applicant = users(:internal_user)
    applicant.user_contracts.create!(agreement_type: :employment, starts_on: (Time.zone.now - 1.month).to_date, month_notice_period: 1)
    applicant_id = applicant.id
    @request.headers['X-Swagger-Sign-In-As'] = applicant_id.to_s
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność/O',
        ends_on: Time.zone.now.to_date,
        examiner_comment: 'test',
        starts_on: Time.zone.now.to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_equal "`Niedostępność/O` leaves are `accepted` automatically.", json_body['examiner_comment']
    end
  end

  def test_Ch_holiday_request_for_user_with_employment_contract_should_be_accepted
    travel_to(Date.new(2025, 2, 25))

    Department.update_all(board_member: false)
    applicant = users(:internal_user)
    applicant.user_contracts.create!(agreement_type: :employment, starts_on: (Time.zone.now - 1.month).to_date, month_notice_period: 1)
    applicant_id = applicant.id
    @request.headers['X-Swagger-Sign-In-As'] = applicant_id.to_s
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność/Ch',
        ends_on: Time.zone.now.to_date,
        examiner_comment: 'test',
        starts_on: Time.zone.now.to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_equal "`Niedostępność/Ch` leaves are `accepted` automatically.", json_body['examiner_comment']
    end
  end

  def test_niedostepnosc_holiday_request_for_user_with_employment_contract_should_not_be_accepted
    travel_to(Date.new(2025, 2, 25))

    Department.update_all(board_member: false)
    applicant = users(:internal_user)
    applicant.user_contracts.create!(agreement_type: :employment, starts_on: (Time.zone.now - 1.month).to_date, month_notice_period: 1)
    applicant_id = applicant.id
    @request.headers['X-Swagger-Sign-In-As'] = applicant_id.to_s
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność',
        ends_on: Time.zone.now.to_date,
        examiner_comment: 'test',
        starts_on: Time.zone.now.to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_nil json_body['accepted_at']
    end
  end

  def test_O_holiday_request_for_user_with_b2b_contract_should_not_be_accepted
    travel_to(Date.new(2025, 2, 25))

    Department.update_all(board_member: false)
    applicant = users(:internal_user)
    applicant.user_contracts.create!(agreement_type: :b2b, starts_on: (Time.zone.now - 1.month).to_date, month_notice_period: 1)
    applicant_id = applicant.id
    @request.headers['X-Swagger-Sign-In-As'] = applicant_id.to_s
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność/O',
        ends_on: Time.zone.now.to_date,
        examiner_comment: 'test',
        starts_on: Time.zone.now.to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_nil json_body['accepted_at']
    end
  end

  def test_email_on_create_is_delivered_with_expected_content_for_board_member_on_autoaccept_for_sickness_created_by_hr
    mkalita_user_alternative = users(:mkalita_user_alternative)
    mkalita_user_alternative.update_columns(department_id: departments(:board_member_department).id)
    applicant = users(:internal_user)
    applicant.update_columns(department_id: departments(:board_member_department).id)
    applicant_id = applicant.id
    creator_id = users(:hr_user).id
    @request.headers['X-Swagger-Sign-In-As'] = creator_id.to_s
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność/Ch',
        ends_on: Time.zone.parse('2014-01-02').to_date,
        starts_on: Time.zone.parse('2014-01-02').to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_equal json_body['applicant_id'], applicant_id
      assert_nil json_body['examiner_id']
      holiday_request = HolidayRequest.find(json_body['id'])
      refute_nil holiday_request.accepted_at
      emails = [
        applicant.email,
        users(:board_member_user).email,
        mkalita_user_alternative.email
      ].flatten
      assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_email_on_create_is_delivered_with_expected_content_for_board_member_on_autoaccept_for_sickness_created_by_applicant
    mkalita_user_alternative = users(:mkalita_user_alternative)
    mkalita_user_alternative.update_columns(department_id: departments(:board_member_department).id)
    applicant = users(:internal_user)
    applicant.update_columns(department_id: departments(:board_member_department).id)
    applicant_id = applicant.id
    @request.headers['X-Swagger-Sign-In-As'] = applicant_id.to_s
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność/Ch',
        ends_on: Time.zone.parse('2014-01-02').to_date,
        starts_on: Time.zone.parse('2014-01-02').to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_equal json_body['applicant_id'], applicant_id
      holiday_request = HolidayRequest.find(json_body['id'])
      refute_nil holiday_request.accepted_at
      emails = [
        users(:board_member_user).email,
        mkalita_user_alternative.email
      ].flatten
      assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_email_on_create_is_delivered_with_expected_content_for_board_member_on_autoaccept_on_vacation
    internal_user = users(:internal_user)
    internal_user.update_columns(department_id: departments(:board_member_department).id)
    applicant = users(:mkalita_user_alternative)
    applicant.update_columns(department_id: departments(:board_member_department).id)
    applicant_id = applicant.id
    creator_id = users(:mkalita_user).id
    @request.headers['X-Swagger-Sign-In-As'] = creator_id.to_s
    perform_enqueued_jobs do
      post :create, params: { holiday_request: {
        applicant_comment: holiday_request.applicant_comment,
        applicant_id: applicant_id,
        category: 'Niedostępność',
        ends_on: Time.zone.parse('2014-01-02').to_date,
        starts_on: Time.zone.parse('2014-01-02').to_date
      } }, format: :json
      assert_response 201, @response.body.to_s
      assert_equal json_body['applicant_id'], applicant_id
      assert_nil json_body['examiner_id']
      holiday_request = HolidayRequest.find(json_body['id'])
      refute_nil holiday_request.accepted_at
      emails = [
        applicant.email,
        users(:board_member_user).email,
        internal_user.email
      ]
      assert_equal emails.sort, ActionMailer::Base.deliveries.to_a.map(&:to).flatten.sort
    end
  end

  def test_email_on_update_is_delivered_with_expected_content_for_accept
    project = projects(:mkalita_project)
    role = roles(:uber_project_manager)
    group = groups(:mkalita_group)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    role = roles(:admin)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    project_manager = users(:mkalita_user)
    project = projects(:mkalita_project)
    role = roles(:pm)
    pmm = project_manager.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    pmm.roles << role
    pmm.reload

    role = roles(:uber_project_manager)
    group = groups(:mkalita_group)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    role = roles(:admin)
    pmm = project_manager.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    pmm.roles << role
    pmm.reload

    assert group.memberships.reload.size >= 1
    assert project_manager.memberships.reload.size >= 1

    role = roles(:dev)
    user = users(:mkalita_user_alternative)
    um = user.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    um.roles << role
    um.reload

    role = roles(:front)
    user = users(:internal_user)
    um = user.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    um.roles << role
    um.reload

    role = roles(:dev)
    user = users(:mkalita_user_alternative)
    um = user.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    um.roles << role
    um.reload

    user.groups << group

    assert user.memberships.reload.size >= 1
    assert user.groups.reload.size >= 1

    user.reload
    project_manager.reload

    holiday_request = HolidayRequest.new(
      applicant_id: user.id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: user.id,
      updated_by_user_id: user.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    perform_enqueued_jobs do
      put :update, params: { id: holiday_request, holiday_request: {
        accept: true
      } }, format: :json
      assert_response 204, @response.body.to_s
      holiday_request.reload
      assert_equal holiday_request.examiner, users(:mkalita_global_admin_programmer)
      emails = [
        holiday_request.applicant.email,
        # users(:mkalita_global_admin_programmer).email, # examiner
        users(:mkalita_user).email # PM
        # # aplikant jako developer bedacy rowniez Uber PMem w tym projekcie nie powinien dostawac drugiego maila
        # users(:mkalita_user_alternative).email # Uber PM przez grupe
      ]
      # Set.new([1,2,3]) == Set.new([1,1,2,3])
      # => true
      # assert_equal Set.new(emails),
      #              Set.new(ActionMailer::Base.deliveries.to_a.map(&:to).flatten)
      assert_equal emails,
                   ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end

    HolidayRequest.destroy_all
    ActionMailer::Base.deliveries.clear

    holiday_request = HolidayRequest.new(
      applicant_id: user.id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: user.id,
      updated_by_user_id: user.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność/B'
    )
    holiday_request.save!
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    perform_enqueued_jobs do
      put :update, params: { id: holiday_request, holiday_request: {
        accept: true
      } }, format: :json
      assert_response 204, @response.body.to_s
      holiday_request.reload
      assert_equal holiday_request.examiner, users(:mkalita_global_admin_programmer)
      emails = [
        holiday_request.applicant.email,
        # users(:mkalita_global_admin_programmer).email, # examiner
        users(:mkalita_user).email # PM
        # aplikant jako developer bedacy rowniez Uber PMem w tym projekcie nie powinien dostawac drugiego maila
        # users(:mkalita_user_alternative).email # Uber PM przez grupe
      ].flatten
      # Set.new([1,2,3]) == Set.new([1,1,2,3])
      # => true
      # assert_equal Set.new(emails),
      #              Set.new(ActionMailer::Base.deliveries.to_a.map(&:to).flatten)
      assert_equal emails,
                   ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_email_on_update_is_delivered_with_expected_content_for_accept_when_applicant_is_also_a_pm_in_his_project
    project = projects(:mkalita_project)
    role = roles(:uber_project_manager)
    group = groups(:mkalita_group)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    role = roles(:admin)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    project_manager = users(:mkalita_user)
    project = projects(:mkalita_project)
    role = roles(:pm)
    pmm = project_manager.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    pmm.roles << role
    pmm.reload

    role = roles(:uber_project_manager)
    group = groups(:mkalita_group)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    role = roles(:admin)
    pmm = project_manager.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    pmm.roles << role
    pmm.reload

    assert group.memberships.reload.size >= 1
    assert project_manager.memberships.reload.size >= 1

    role = roles(:dev)
    user = users(:mkalita_user_alternative)
    um = user.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    um.roles << role
    um.reload

    role = roles(:front)
    user = users(:internal_user)
    um = user.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    um.roles << role
    um.reload

    roles = [roles(:uber_project_manager), roles(:dev)]
    role_ids = roles.map(&:id)
    user = users(:mkalita_user_alternative)
    um = user.memberships.create_with(role_ids: role_ids).find_or_create_by!(
      project_id: project.id
    )
    um.roles << roles
    um.reload

    user.groups << group

    assert user.memberships.reload.size >= 1
    assert user.groups.reload.size >= 1

    user.reload
    project_manager.reload

    holiday_request = HolidayRequest.new(
      applicant_id: user.id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: user.id,
      updated_by_user_id: user.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    perform_enqueued_jobs do
      put :update, params: { id: holiday_request, holiday_request: {
        accept: true
      } }, format: :json
      assert_response 204, @response.body.to_s
      holiday_request.reload
      assert_equal holiday_request.examiner, users(:mkalita_global_admin_programmer)
      emails = [
        holiday_request.applicant.email,
        # users(:mkalita_global_admin_programmer).email, # examiner
        users(:mkalita_user).email # PM
        # aplikant jako developer bedacy rowniez Uber PMem w tym projekcie nie powinien dostawac drugiego maila
        # users(:mkalita_user_alternative).email # Uber PM przez grupe
      ]
      # Set.new([1,2,3]) == Set.new([1,1,2,3])
      # => true
      # assert_equal Set.new(emails),
      #              Set.new(ActionMailer::Base.deliveries.to_a.map(&:to).flatten)
      assert_equal emails,
                   ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end

    HolidayRequest.destroy_all
    ActionMailer::Base.deliveries.clear

    holiday_request = HolidayRequest.new(
      applicant_id: user.id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: user.id,
      updated_by_user_id: user.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność/B'
    )
    holiday_request.save!
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    perform_enqueued_jobs do
      put :update, params: { id: holiday_request, holiday_request: {
        accept: true
      } }, format: :json
      assert_response 204, @response.body.to_s
      holiday_request.reload
      assert_equal holiday_request.examiner, users(:mkalita_global_admin_programmer)
      emails = [
        holiday_request.applicant.email,
        # users(:mkalita_global_admin_programmer).email, # examiner
        users(:mkalita_user).email # PM
        # aplikant jako developer bedacy rowniez Uber PMem w tym projekcie nie powinien dostawac drugiego maila
        # users(:mkalita_user_alternative).email # Uber PM przez grupe
      ].flatten
      # Set.new([1,2,3]) == Set.new([1,1,2,3])
      # => true
      # assert_equal Set.new(emails),
      #              Set.new(ActionMailer::Base.deliveries.to_a.map(&:to).flatten)
      assert_equal emails,
                   ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_email_on_update_is_delivered_with_expected_content_for_accept_if_current_user_is_same_as_examiner
    project = projects(:mkalita_project)
    role = roles(:uber_project_manager)
    group = groups(:mkalita_group)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    role = roles(:admin)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    project_manager = users(:mkalita_user)
    project = projects(:mkalita_project)
    role = roles(:pm)
    pmm = project_manager.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    pmm.roles << role
    pmm.reload

    role = roles(:uber_project_manager)
    group = groups(:mkalita_group)
    gm = group.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    gm.roles << role
    gm.reload

    role = roles(:admin)
    pmm = project_manager.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    pmm.roles << role
    pmm.reload

    assert group.memberships.reload.size >= 1
    assert project_manager.memberships.reload.size >= 1

    role = roles(:dev)
    user = users(:mkalita_user_alternative)
    um = user.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    um.roles << role
    um.reload

    role = roles(:front)
    user = users(:internal_user)
    um = user.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    um.roles << role
    um.reload

    role = roles(:dev)
    user = users(:mkalita_user_alternative)
    um = user.memberships.create_with(role_ids: [role.id]).find_or_create_by!(
      project_id: project.id
    )
    um.roles << role
    um.reload

    user.groups << group

    assert user.memberships.reload.size >= 1
    assert user.groups.reload.size >= 1

    user.reload
    project_manager.reload

    holiday_request = HolidayRequest.new(
      applicant_id: user.id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: user.id,
      updated_by_user_id: user.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność'
    )
    holiday_request.save!
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    perform_enqueued_jobs do
      put :update, params: { id: holiday_request, holiday_request: {
        accept: true
      } }, format: :json
      assert_response 204, @response.body.to_s
      holiday_request.reload
      assert_equal holiday_request.examiner, users(:mkalita_global_admin_programmer)
      emails = [
        holiday_request.applicant.email,
        users(:mkalita_user).email # PM
        # aplikant jako developer bedacy rowniez Uber PMem w tym projekcie nie powinien dostawac drugiego maila
        # users(:mkalita_user_alternative).email # Uber PM przez grupe
      ]
      # Set.new([1,2,3]) == Set.new([1,1,2,3])
      # => true
      # assert_equal Set.new(emails),
      #              Set.new(ActionMailer::Base.deliveries.to_a.map(&:to).flatten)
      assert_equal emails,
                   ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end

    HolidayRequest.destroy_all
    ActionMailer::Base.deliveries.clear

    holiday_request = HolidayRequest.new(
      applicant_id: user.id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: user.id,
      updated_by_user_id: user.id,
      modified_by_user_at: Time.zone.now,
      category: 'Niedostępność/B'
    )
    holiday_request.save!
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    perform_enqueued_jobs do
      put :update, params: { id: holiday_request, holiday_request: {
        accept: true
      } }, format: :json
      assert_response 204, @response.body.to_s
      holiday_request.reload
      assert_equal holiday_request.examiner, users(:mkalita_global_admin_programmer)
      emails = [
        holiday_request.applicant.email,
        users(:mkalita_user).email # PM
        # aplikant jako developer bedacy rowniez Uber PMem w tym projekcie nie powinien dostawac drugiego maila
        # users(:mkalita_user_alternative).email # Uber PM przez grupe
      ].flatten
      # Set.new([1,2,3]) == Set.new([1,1,2,3])
      # => true
      # assert_equal Set.new(emails),
      #              Set.new(ActionMailer::Base.deliveries.to_a.map(&:to).flatten)
      assert_equal emails,
                   ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_email_on_update_is_delivered_with_expected_content_for_reject
    holiday_request = HolidayRequest.new(
      applicant_id: users(:mkalita_user_alternative).id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: users(:mkalita_user_alternative).id,
      updated_by_user_id: users(:mkalita_user_alternative).id,
      modified_by_user_at: Time.zone.now
    )
    holiday_request.save!
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    perform_enqueued_jobs do
      put :update, params: { id: holiday_request, holiday_request: {
        reject: true,
        examiner_id: users(:mkalita_global_admin_programmer).id
      } }, format: :json
      assert_response 204, @response.body.to_s
      holiday_request.reload
      assert_equal holiday_request.examiner, users(:mkalita_global_admin_programmer)
      emails = [
        holiday_request.applicant.email
      ]
      assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_email_on_update_is_delivered_with_expected_content_for_edit
    holiday_request = HolidayRequest.new(
      applicant_id: users(:mkalita_user_alternative).id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: users(:mkalita_user_alternative).id,
      updated_by_user_id: users(:mkalita_user_alternative).id,
      modified_by_user_at: Time.zone.now
    )
    holiday_request.save!
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    perform_enqueued_jobs do
      put :update, params: { id: holiday_request, holiday_request: {
        examiner_comment: 'Some change'
      } }, format: :json
      assert_response 204, @response.body.to_s
      holiday_request.reload
      assert_nil holiday_request.examiner
      emails = [
        holiday_request.applicant.email
      ]
      assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_show
    @request.headers['X-Swagger-Sign-In-As'] = holiday_request.applicant_id.to_s
    get :show, params: { id: holiday_request.id }, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'url'
    assert_equal 2016, holiday_request.starts_on.year
    assert_includes json_body, 'problems'
    assert_includes json_body['problems'], 'Do not submit holiday request for days before the year 2017! Report time in Redmine.'
  end

  def test_show_with_attachment
    user = users(:wiktoria)
    authenticate(user)
    original_file = File.open(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'))
    tempfile = Tempfile.new('jpg_705kb.jpg')
    tempfile.write(original_file.read)
    holiday_request.file = tempfile
    holiday_request.category = 'Niedostępność/O'
    holiday_request.save(validate: false)

    get :show, params: { id: holiday_request }, format: :json

    assert_response :success
    assert_not_empty json_body['file_name']
  end

  def test_show_with_attachment_for_all_users
    user = users(:milosz)
    authenticate(user)
    original_file = File.open(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'))
    tempfile = Tempfile.new('jpg_705kb.jpg')
    tempfile.write(original_file.read)
    holiday_request.file = tempfile
    holiday_request.category = 'Niedostępność/O'
    holiday_request.save(validate: false)

    get :show, params: { id: holiday_request }, format: :json

    assert_response :success
    assert_not_empty json_body['file_name']
  end

  def test_update_fresh_holiday_request
    holiday_request.update_columns(accepted_at: nil, rejected_at: nil)
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    put :update, params: { id: holiday_request, holiday_request: {
      accepted_at: nil,
      applicant_comment: holiday_request.applicant_comment,
      applicant_id: holiday_request.applicant_id,
      category: holiday_request.category,
      ends_on: holiday_request.ends_on + 25.day,
      examiner_comment: holiday_request.examiner_comment,
      examiner_id: holiday_request.examiner_id,
      rejected_at: nil,
      starts_on: holiday_request.starts_on + 25.day,
      visible: holiday_request.visible
    } }, format: :json
    assert_response 204, @response.body.to_s
  end

  def test_email_on_convert_to_is_delivered_with_expected_content
    holiday_request = HolidayRequest.new(
      applicant_id: users(:mkalita_user_alternative).id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: users(:mkalita_user_alternative).id,
      updated_by_user_id: users(:mkalita_user_alternative).id,
      modified_by_user_at: Time.zone.now
    )
    holiday_request.save!
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    perform_enqueued_jobs do
      put :update, params: { id: holiday_request, holiday_request: {
        convert_to: 'Niedostępność/O'
      } }, format: :json
      assert_response 204, @response.body.to_s
      holiday_request.reload
      emails = [
        holiday_request.applicant.email
      ]
      assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
      ActionMailer::Base.deliveries.to_a.all? { |delivery| assert_match(/zaktualizowne/, delivery.subject) }
      assert_match(/zmienione/, ActionMailer::Base.deliveries.to_a.first.body.parts.select { |part| part.content_type =~ /text\/html/ }.first.body.to_s)
    end
  end

  def test_update_rejected_holiday_request
    holiday_request = HolidayRequest.new(
      applicant_id: users(:mkalita_user_alternative).id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: users(:mkalita_user_alternative).id,
      updated_by_user_id: users(:mkalita_user_alternative).id,
      modified_by_user_at: Time.zone.now
    )
    holiday_request.save!
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    perform_enqueued_jobs do
      put :update, params: { id: holiday_request, holiday_request: {
        ends_on: '2040-01-07'
      } }, format: :json
      assert_response 204, @response.body.to_s
      holiday_request.reload
      emails = [
        holiday_request.applicant.email
      ]
      assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
      ActionMailer::Base.deliveries.to_a.all? { |delivery| assert_match(/zaktualizowny/, delivery.subject) }
      assert_match(/zmienione/, ActionMailer::Base.deliveries.to_a.first.body.parts.select { |part| part.content_type =~ /text\/html/ }.first.body.to_s)
    end
  end

  def test_update_allowed_on_rejected_holiday_request
    holiday_request.update_columns(accepted_at: nil, rejected_at: Time.zone.now)
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    put :update, params: { id: holiday_request, holiday_request: {
      accepted_at: nil,
      applicant_comment: holiday_request.applicant_comment,
      applicant_id: holiday_request.applicant_id,
      category: holiday_request.category,
      ends_on: holiday_request.ends_on,
      examiner_comment: holiday_request.examiner_comment,
      examiner_id: holiday_request.examiner_id,
      rejected_at: Time.zone.now,
      starts_on: holiday_request.starts_on,
      visible: holiday_request.visible
    } }, format: :json
    assert_response 204, @response.body.to_s
  end

  def test_update_allowed_on_accepted_holiday_request_in_the_future_if_applicant_but_not_creator_nor_admin_nor_examiner
    creator = users(:mkalita_user_alternative)

    user = users(:internal_user)
    user.update_columns(department_id: departments(:one).id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    user.reload

    holiday_request = HolidayRequest.create!(
      applicant_id: user.id,
      starts_on: '2040-01-01',
      ends_on: '2040-01-14',
      created_by_user_id: creator.id,
      updated_by_user_id: creator.id,
      modified_by_user_at: Time.zone.now,
      accepted_at: Time.zone.now,
      rejected_at: nil
    )

    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    put :update, params: { id: holiday_request, holiday_request: {
      accepted_at: Time.zone.now,
      applicant_comment: 'some new text',
      applicant_id: holiday_request.applicant_id,
      category: holiday_request.category,
      ends_on: holiday_request.ends_on,
      examiner_comment: holiday_request.examiner_comment,
      examiner_id: holiday_request.examiner_id,
      rejected_at: nil,
      starts_on: holiday_request.starts_on,
      visible: holiday_request.visible
    } }, format: :json

    assert_response 204, @response.body.to_s
    assert_equal 'some new text', holiday_request.reload.applicant_comment
  end

  def test_update_denied_on_accepted_holiday_request_in_the_past_with_start_on_changed
    creator = users(:mkalita_user_alternative)

    user = users(:internal_user)
    user.update_columns(department_id: departments(:one).id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    user.reload

    holiday_request = HolidayRequest.create!(
      applicant_id: user.id,
      starts_on: '2000-01-01',
      ends_on: '2000-01-14',
      created_by_user_id: creator.id,
      updated_by_user_id: creator.id,
      modified_by_user_at: Time.zone.now,
      accepted_at: Time.zone.now,
      rejected_at: nil
    )

    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    put :update, params: { id: holiday_request, holiday_request: {
      accepted_at: Time.zone.now,
      applicant_comment: 'some new text',
      applicant_id: holiday_request.applicant_id,
      category: holiday_request.category,
      ends_on: holiday_request.ends_on,
      examiner_comment: holiday_request.examiner_comment,
      examiner_id: holiday_request.examiner_id,
      rejected_at: nil,
      starts_on: holiday_request.starts_on - 1.week,
      visible: holiday_request.visible
    } }, format: :json

    assert_response 422, @response.body.to_s
    assert_includes json_body, 'errors'
    assert_equal ['Applicant can only change the start date of an accepted (upcoming) holiday to be in the future!'],
                 json_body['errors']['starts_on']
  end

  def test_update_denied_on_accepted_holiday_request_in_the_past_if_applicant_but_not_creator_nor_admin_nor_examiner
    creator = users(:mkalita_user_alternative)

    user = users(:internal_user)
    user.update_columns(department_id: departments(:one).id)
    user.global_roles.clear
    user.global_roles << global_roles(:global_user)
    user.reload

    holiday_request = HolidayRequest.create!(
      applicant_id: user.id,
      starts_on: '2000-01-01',
      ends_on: '2000-01-14',
      created_by_user_id: creator.id,
      updated_by_user_id: creator.id,
      modified_by_user_at: Time.zone.now,
      accepted_at: Time.zone.now,
      rejected_at: nil
    )

    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
    put :update, params: { id: holiday_request, holiday_request: {
      accepted_at: Time.zone.now,
      applicant_comment: 'some new text',
      applicant_id: holiday_request.applicant_id,
      category: holiday_request.category,
      ends_on: holiday_request.ends_on,
      examiner_comment: holiday_request.examiner_comment,
      examiner_id: holiday_request.examiner_id,
      rejected_at: nil,
      starts_on: holiday_request.starts_on,
      visible: holiday_request.visible
    } }, format: :json

    assert_response 422, @response.body.to_s
    assert_includes json_body, 'errors'
    assert_equal ['Applicant cannot edit this accepted holiday request. It is possible only for future holidays of cat. `Niedostępność`/`O`/`B`/`Ch`.'], json_body['errors']['base']
  end

  def test_update_accept_nz_under_limit_succeeds
    travel_to Time.zone.parse('2040-02-06') do
      creator = users(:mkalita_user_alternative)
      user = users(:mkalita_global_admin_programmer)

      holiday_request = HolidayRequest.create!(
        applicant_id: user.id,
        starts_on: '2040-02-06',
        ends_on: '2040-02-09',
        category: 'Niedostępność/Ż',
        created_by_user_id: creator.id,
        updated_by_user_id: creator.id,
        modified_by_user_at: Time.zone.now,
        accepted_at: nil,
        rejected_at: nil
      )
      assert_equal 4, holiday_request.business_days.size

      user.update_columns(password_changed_at: Time.zone.now)
      @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
      assert_difference(-> { HolidayRequest.last.absences.where(visible: true).count }, 4) do
        put :update, params: { id: holiday_request, holiday_request: {
          accept: true,
          skip_warning: true
        } }, format: :json
      end
      assert_response 204, @response.body.to_s
      refute_nil holiday_request.reload.accepted_at
    end
  end

  def test_update_accept_nz_under_limit_succeeds_with_file
    travel_to Time.zone.parse('2040-02-06') do
      creator = users(:mkalita_user_alternative)
      user = users(:mkalita_global_admin_programmer)

      holiday_request = HolidayRequest.create!(
        applicant_id: user.id,
        starts_on: '2040-02-06',
        ends_on: '2040-02-09',
        category: 'Niedostępność/Ż',
        created_by_user_id: creator.id,
        updated_by_user_id: creator.id,
        modified_by_user_at: Time.zone.now,
        accepted_at: nil,
        rejected_at: nil
      )
      assert_equal 4, holiday_request.business_days.size

      file = File.read(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'))
      base64_content = Base64.encode64(file)

      user.update_columns(password_changed_at: Time.zone.now)
      @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
      assert_difference(-> { HolidayRequest.last.absences.where(visible: true).count }, 4) do
        put :update, params: { id: holiday_request, holiday_request: {
          accept: true,
          skip_warning: true,
          file: {
            filetype: 'image/png',
            filename: 'Screenshot',
            filesize: file.size,
            base64: base64_content
          }
        } }, format: :json
      end
      assert_response 204, @response.body.to_s
      assert_not_nil holiday_request.reload.accepted_at
      assert holiday_request.file.present?
    end
  end

  def test_update_accept_nz_over_limit_succeeds_if_skip_warning_param_is_true_and_email_to_hr_is_delivered_with_expected_content
    perform_enqueued_jobs do
      user = users(:mkalita_global_admin_programmer)
      creator = users(:mkalita_user_alternative)

      holiday_request = HolidayRequest.create!(
        applicant_id: creator.id,
        starts_on: '2040-02-06',
        ends_on: '2040-02-10',
        category: 'Niedostępność/Ż',
        created_by_user_id: creator.id,
        updated_by_user_id: creator.id,
        modified_by_user_at: Time.zone.now,
        accepted_at: nil,
        rejected_at: nil
      )
      assert_equal 5, holiday_request.business_days.size

      holiday_request_id = holiday_request.id

      travel_to Time.zone.parse('2040-02-06') do
        holiday_request = HolidayRequest.find(holiday_request_id)

        user = users(:mkalita_global_admin_programmer)

        user.update_columns(password_changed_at: Time.zone.now)
        @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
        assert_difference(-> { HolidayRequest.last.absences.where(visible: true).count }, 5) do
          put :update, params: { id: holiday_request, holiday_request: {
            accept: true,
            skip_warning: true
          } }, format: :json
        end
        assert_response 204, @response.body.to_s
        refute_nil holiday_request.reload.accepted_at
        emails = [
          holiday_request.applicant.email,
        ].flatten
        assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
        first_delivery = ActionMailer::Base.deliveries.first
        refute_match(/Przekroczony/, first_delivery.subject)
        refute_match(/przekroczony/, first_delivery.body.parts.select { |part| part.content_type =~ /text\/html/ }.first.body.to_s)
        last_delivery = ActionMailer::Base.deliveries.last
        assert_match(/zaakceptowany/, last_delivery.subject)
        assert_match(/zaakceptowany/, last_delivery.body.parts.select { |part| part.content_type =~ /text\/html/ }.first.body.to_s)
      end
    end
  end

  def test_update_nz_accept_over_limit_fails_with_error
    user = users(:mkalita_global_admin_programmer)
    creator = users(:mkalita_user_alternative)
    holiday_request = HolidayRequest.create!(
      applicant_id: user.id,
      starts_on: '2040-02-06',
      ends_on: '2040-02-10',
      category: 'Niedostępność/Ż',
      created_by_user_id: creator.id,
      updated_by_user_id: creator.id,
      modified_by_user_at: Time.zone.now,
      accepted_at: nil,
      rejected_at: nil
    )
    assert_equal 5, holiday_request.business_days.size

    holiday_request_id = holiday_request.id

    travel_to Time.zone.parse('2040-02-06') do
      holiday_request = HolidayRequest.find(holiday_request_id)

      user = users(:mkalita_global_admin_programmer)

      user.update_columns(password_changed_at: Time.zone.now)
      @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s
      assert_no_difference(-> { HolidayRequest.last.absences.where(visible: true).count }) do
        put :update, params: { id: holiday_request, holiday_request: {
          accept: true
        } }, format: :json
      end
      assert_response 422, @response.body.to_s
      assert_includes json_body, 'errors'
      assert_equal ["Warning! This request exceeds the limit of 'Niedostępność/Ż' absences for this user."], json_body['errors']['limit_exceeded']
    end
  end

  def test_accept_with_existing_overlapping_holiday_request
    user = users(:wiktoria)
    holiday_request = HolidayRequest.new(
      category: 'Niedostępność',
      applicant: user,
      starts_on: Time.zone.today.beginning_of_month,
      ends_on: Time.zone.today.beginning_of_month + 2.days
    )
    holiday_request.save(validate: false)
    @request.headers['X-Swagger-Sign-In-As'] = user.id.to_s

    put :update, params: { id: holiday_request, holiday_request: { accept: true } }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body['errors']['starts_on']
    assert_not_empty json_body['errors']['ends_on']
  end

  def test_destroy
    date = Time.zone.parse('2015-12-14').to_date
    new_holiday_request = HolidayRequest.create(
      starts_on: date,
      ends_on: date,
      applicant_id: users(:mkalita_global_admin_programmer).id,
      category: 'Niedostępność/Ż',
      created_by_user_id: users(:mkalita_global_admin_programmer).id,
      updated_by_user_id: users(:mkalita_global_admin_programmer).id,
      modified_by_user_at: Time.zone.now
    )
    assert new_holiday_request.persisted?
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    assert_difference('HolidayRequest.count', -1) do
      delete :destroy, format: :json, params: { id: new_holiday_request.id }
      assert_response 204, @response.body.to_s
    end
  end

  def test_email_on_destroy_of_sick_or_occasional_category_holiday_request_is_delivered_also_to_hr_with_expected_content_if_submitted_for_common_user_applicant_by_an_entitled_user
    perform_enqueued_jobs do
      date = Time.zone.parse('2015-12-14').to_date
      holiday_request = HolidayRequest.create(
        starts_on: date,
        ends_on: date,
        applicant_id: users(:mkalita_user).id,
        category: 'Niedostępność/Ch',
        created_by_user_id: users(:mkalita_global_admin_programmer).id,
        updated_by_user_id: users(:mkalita_global_admin_programmer).id,
        modified_by_user_at: Time.zone.now
      )
      assert holiday_request.persisted?
      @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
      assert_difference('HolidayRequest.count', -1) do
        delete :destroy, format: :json, params: { id: holiday_request.id }
        assert_response 204, @response.body.to_s
      end
      emails = [
        holiday_request.applicant.email
      ].flatten
      assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
      ActionMailer::Base.deliveries.to_a.all? { |delivery| assert_match(/usunięty/, delivery.subject) }
      assert_match(/usunięty/, ActionMailer::Base.deliveries.to_a.first.body.parts.select { |part| part.content_type =~ /text\/html/ }.first.body.to_s)
    end
  end

  def test_email_on_destroy_is_delivered_with_expected_content_if_submitted_for_common_user_applicant_by_an_entitled_user
    perform_enqueued_jobs do
      date = Time.zone.parse('2015-12-14').to_date
      holiday_request = HolidayRequest.create(
        starts_on: date,
        ends_on: date,
        applicant_id: users(:mkalita_user).id,
        category: 'Niedostępność/Ż',
        created_by_user_id: users(:mkalita_global_admin_programmer).id,
        updated_by_user_id: users(:mkalita_global_admin_programmer).id,
        modified_by_user_at: Time.zone.now
      )
      assert holiday_request.persisted?
      @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
      assert_difference('HolidayRequest.count', -1) do
        delete :destroy, format: :json, params: { id: holiday_request.id }
        assert_response 204, @response.body.to_s
      end
      emails = [
        holiday_request.applicant.email
      ]
      assert_equal emails, ActionMailer::Base.deliveries.to_a.map(&:to).flatten
      ActionMailer::Base.deliveries.to_a.all? { |delivery| assert_match(/usunięty/, delivery.subject) }
      assert_match(/usunięty/, ActionMailer::Base.deliveries.to_a.first.body.parts.select { |part| part.content_type =~ /text\/html/ }.first.body.to_s)
    end
  end

  def test_email_on_destroy_is_not_delivered_with_expected_content_if_submitted_for_applicant_by_himself_as_an_entitled_user
    perform_enqueued_jobs do
      date = Time.zone.parse('2015-12-14').to_date
      holiday_request = HolidayRequest.create(
        starts_on: date,
        ends_on: date,
        applicant_id: users(:mkalita_global_admin_programmer).id,
        category: 'Niedostępność/Ż',
        created_by_user_id: users(:mkalita_global_admin_programmer).id,
        updated_by_user_id: users(:mkalita_global_admin_programmer).id,
        modified_by_user_at: Time.zone.now
      )
      assert holiday_request.persisted?
      @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
      assert_difference('HolidayRequest.count', -1) do
        delete :destroy, format: :json, params: { id: holiday_request.id }
        assert_response 204, @response.body.to_s
      end
      assert_empty ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_applicant_can_destroy_accepted_if_has_special_powers
    time = Time.zone.parse('2015-12-14')
    date = time.to_date
    new_holiday_request = HolidayRequest.create(
      accepted_at: time,
      examiner_id: users(:mkalita_global_admin_programmer).id,
      starts_on: date,
      ends_on: date,
      applicant_id: users(:mkalita_global_admin_programmer).id,
      category: 'Niedostępność',
      created_by_user_id: users(:mkalita_global_admin_programmer).id,
      updated_by_user_id: users(:mkalita_global_admin_programmer).id,
      modified_by_user_at: time
    )
    assert holiday_request.persisted?
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_global_admin_programmer).id.to_s
    assert_difference('HolidayRequest.count', -1) do
      delete :destroy, format: :json, params: { id: new_holiday_request.id }
      assert_response 204, @response.body.to_s
    end
  end

  def test_applicant_cannot_destroy_accepted
    perform_enqueued_jobs do
      time = Time.zone.parse('2015-12-14')
      date = time.to_date
      new_holiday_request = HolidayRequest.create(
        accepted_at: time,
        examiner_id: users(:mkalita_global_admin_programmer).id,
        starts_on: date,
        ends_on: date,
        applicant_id: users(:internal_user).id,
        category: 'Niedostępność',
        created_by_user_id: users(:mkalita_global_admin_programmer).id,
        updated_by_user_id: users(:mkalita_global_admin_programmer).id,
        modified_by_user_at: time
      )
      assert new_holiday_request.persisted?
      @request.headers['X-Swagger-Sign-In-As'] = users(:internal_user).id.to_s
      assert_no_difference('HolidayRequest.count') do
        delete :destroy, format: :json, params: { id: new_holiday_request.id }
        assert_response 422, @response.body.to_s
        assert_includes json_body, 'errors'
        assert_equal ['Applicant cannot delete an accepted holiday request!'], json_body['errors']['base']
      end
      assert_empty ActionMailer::Base.deliveries.to_a.map(&:to).flatten
    end
  end

  def test_users_authorization
    user = users(:mkalita_user)
    policy = stub(users?: false)
    HolidayRequestPolicy.stubs(:new).with(user, HolidayRequest).returns(policy)
    sign_in(user)
    get :users, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_index_authorization
    user = users(:mkalita_user)
    policy = stub(index?: false)
    HolidayRequestPolicy.stubs(:new).with(user, HolidayRequest).returns(policy)
    sign_in(user)
    get :index, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_show_authorization
    user = users(:mkalita_user)
    holiday_request = holiday_requests(:one)
    policy = stub(show?: false)
    HolidayRequestPolicy.stubs(:new).with(user, holiday_request).returns(policy)
    sign_in(user)
    get :show, params: { id: holiday_request.id }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_create_authorization
    user = users(:mkalita_user)
    HolidayRequestPolicy.any_instance.stubs(:create?).returns(false)
    sign_in(user)
    date = Time.zone.parse('2014-01-02').to_date
    post :create,
          params: { holiday_request: {
            accepted_at: nil,
            applicant_comment: '',
            applicant_id: holiday_request.applicant_id,
            category: holiday_request.category,
            ends_on: date,
            examiner_comment: '',
            examiner_id: nil,
            rejected_at: nil,
            starts_on: date,
            visible: false
          } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_update_authorization
    user = users(:mkalita_user)
    holiday_request = holiday_requests(:one)
    policy = stub(update?: false)
    HolidayRequestPolicy.stubs(:new).with(user, holiday_request).returns(policy)
    sign_in(user)
    patch :update, params: { id: holiday_request.id, holiday_request: {
      accepted_at: holiday_request.accepted_at,
      applicant_comment: holiday_request.applicant_comment,
      applicant_id: holiday_request.applicant_id,
      category: holiday_request.category,
      ends_on: holiday_request.ends_on,
      examiner_comment: holiday_request.examiner_comment,
      examiner_id: holiday_request.examiner_id,
      rejected_at: holiday_request.rejected_at,
      starts_on: holiday_request.starts_on,
      visible: holiday_request.visible
    } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_update_changes_state_to_pending_if_ends_on_changed
    @request.headers['X-Swagger-Sign-In-As'] = nil
    applicant = users(:milosz)
    applicant.update_column(:absence_balance, 26)
    examiner = users(:wiktoria)
    HolidayRequest.destroy_all
    travel_to Time.new(2024, 5, 9, 12, 0, 0)

    holiday_request = HolidayRequest.create!(
      applicant:,
      starts_on: 1.week.from_now.to_date,
      ends_on: 2.weeks.from_now.to_date,
      non_working_day_request: true,
      accepted_at: Time.current,
      examiner:,
      visible: true,
      category: 'Niedostępność',
      created_by_user_id: applicant.id,
      updated_by_user_id: examiner.id,
      modified_by_user_at: Time.current
    )
    authenticate(applicant)

    assert_difference -> { applicant.reload.absence_balance }, 8 do
      $pry = true
      patch :update, params: { id: holiday_request, holiday_request: { ends_on: 3.weeks.from_now.to_date } },
            format: :json
      $pry = false
      assert holiday_request.reload.pending?
    end
  end

  def test_update_changes_state_to_pending_if_non_working_day_request_changed
    @request.headers['X-Swagger-Sign-In-As'] = nil
    applicant = users(:milosz)
    applicant.update_column(:absence_balance, 26)
    examiner = users(:wiktoria)
    HolidayRequest.destroy_all
    travel_to Time.new(2024, 5, 9, 12, 0, 0)

    holiday_request = HolidayRequest.create!(
      applicant:,
      starts_on: 1.week.from_now.to_date,
      ends_on: 2.weeks.from_now.to_date,
      non_working_day_request: true,
      accepted_at: Time.current,
      examiner:,
      visible: true,
      category: 'Niedostępność',
      created_by_user_id: applicant.id,
      updated_by_user_id: examiner.id,
      modified_by_user_at: Time.current
    )
    authenticate(applicant)

    assert_difference -> { applicant.reload.absence_balance }, 8 do
      patch :update, params: { id: holiday_request, holiday_request: { non_working_day_request: false } }, format: :json
      assert holiday_request.reload.pending?
    end
  end

  def test_update_changes_absence_balance_properly_if_non_working_day_request_changed_by_examiner
    @request.headers['X-Swagger-Sign-In-As'] = nil
    applicant = users(:milosz)
    applicant.update_column(:absence_balance, 26)
    examiner = users(:wiktoria)
    HolidayRequest.destroy_all
    travel_to Time.new(2024, 5, 9, 12, 0, 0)

    holiday_request = HolidayRequest.create!(
      applicant:,
      starts_on: 1.week.from_now.to_date,
      ends_on: 2.weeks.from_now.to_date,
      non_working_day_request: true,
      accepted_at: Time.current,
      examiner:,
      visible: true,
      category: 'Niedostępność',
      created_by_user_id: applicant.id,
      updated_by_user_id: examiner.id,
      modified_by_user_at: Time.current
    )
    authenticate(examiner)

    assert_difference -> { applicant.reload.absence_balance }, 2 do
      patch :update, params: { id: holiday_request, holiday_request: { non_working_day_request: false } }, format: :json
    end
  end

  def test_destroy_authorization
    user = users(:mkalita_user)
    holiday_request = holiday_requests(:one)
    policy = stub(destroy?: false)
    HolidayRequestPolicy.stubs(:new).with(user, holiday_request).returns(policy)
    sign_in(user)
    delete :destroy, params: { id: holiday_request.id }, format: :json
    assert_response 403, @response.body.to_s
  end
end
