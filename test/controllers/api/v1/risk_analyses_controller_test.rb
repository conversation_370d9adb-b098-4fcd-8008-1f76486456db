require "test_helper"

class Api::V1::RiskAnalysesControllerTest < ActionController::TestCase
  let(:admin_user) { users(:mkalita_user) }
  let(:regular_user) { users(:milosz) }
  let(:risk_analysis) { risk_analyses(:one) }
  let(:valid_params) do
    {
      risk_analysis: {
        name: "Test Risk Analysis",
        company_id: companies(:one).id,
        registry_activity_id: registry_activities(:one).id,
        creation_date: Date.today.to_s,
        property: "confidentiality",
        danger: "Unauthorized access to personal data",
        vulnerability_description: "Weak password policies and lack of two-factor authentication",
        security: "Implement strong password policies and enable two-factor authentication",
        probability: "possible",
        effect: "high",
        data_processing_impact_assessment: "required"
      }
    }
  end
  let(:invalid_params) do
    {
      risk_analysis: {
        name: nil,
        company_id: companies(:one).id,
        registry_activity_id: registry_activities(:one).id,
        creation_date: Date.today.to_s,
        property: "confidentiality"
      }
    }
  end
  let(:update_params) { { id: risk_analysis.id, risk_analysis: { name: "Updated Risk Analysis" } } }
  let(:invalid_update_params) { { id: risk_analysis.id, risk_analysis: { name: nil } } }

  setup do
    authenticate(admin_user)
  end

  test "returns risk analyses - index" do
    get :index, params: {}, format: :json

    assert_response :success
    assert_equal RiskAnalysis.count, json_body.size
  end

  test "returns forbidden for regular user - index" do
    authenticate(regular_user)
    get :index, format: :json

    assert_response :forbidden
  end

  test "returns risk analysis - show" do
    get :show, params: { id: risk_analysis.id }, format: :json

    assert_response :success
    assert_equal risk_analysis.id, json_body["risk_analysis"]["id"]
  end

  test "creates risk analysis - create" do
    assert_difference('RiskAnalysis.count', 1) do
      post :create, params: valid_params, format: :json
    end

    assert_response :success
    assert_equal valid_params[:risk_analysis][:name], json_body['name']
    assert_equal "high", json_body['risk']
  end

  test "updates risk analysis - update" do
    patch :update, params: update_params, format: :json

    assert_response :success
    assert_equal update_params[:risk_analysis][:name], risk_analysis.reload.name
  end

  test "destroys risk analysis - destroy" do
    assert_difference('RiskAnalysis.count', -1) do
      delete :destroy, params: { id: risk_analysis.id }, format: :json
    end

    assert_response :no_content
  end

  test "returns error for create with invalid parameters" do
    assert_no_difference('RiskAnalysis.count') do
      post :create, params: invalid_params, format: :json
    end

    assert_response :unprocessable_entity
    assert_includes json_body["errors"].keys, "name"
  end

  test "returns error for update with invalid parameters" do
    patch :update, params: invalid_update_params, format: :json

    assert_response :unprocessable_entity
    assert_includes json_body["errors"].keys, "name"
  end

  test "returns not found for non-existent record - show" do
    get :show, params: { id: 999999 }, format: :json

    assert_response :not_found
    assert_not_empty json_body["errors"]
  end

  test "filters risk analyses by term" do
    get :index, params: { f: { term: 'Marketing' } }, format: :json

    assert_response :success
    assert json_body.all? { |ra| ra['name'].include?('Marketing') }
  end

  test "filters risk analyses by company_id" do
    company_id = companies(:one).id
    get :index, params: { f: { company_id: company_id } }, format: :json

    assert_response :success
    assert RiskAnalysis.where(id: json_body.map { |el| el['id'] }).all? { |ra| ra.company_id == company_id }
  end

  test "filters risk analyses by state" do
    get :index, params: { f: { state: 'active' } }, format: :json

    assert_response :success
    assert RiskAnalysis.includes(:registry_activity).where(id: json_body.map { |el| el['id'] }).all? { |ra| ra.registry_activity.state == 'active' }
  end

  test "paginates results" do
    get :index, params: { page: 2, per_page: 3 }, format: :json

    assert_response :success
    assert_equal json_body.size, 2
  end
end
