require "test_helper"

class Api::V1::CheckAbsencesControllerTest < ActionController::TestCase
  fixtures :absences

  setup do
    @request.headers['X-Redmine-Absences-Check-API-Key'] = Settings.redmine_api['redmine_absences_check_api_key']
  end

  def test_checked_absences_found
    post :check, params: { user_id: absences(:one).user_id,
                           dates: "2016-07-20,2016-07-21,2016-07-22,2016-07-23,2016-07-24" },
                 format: :json
    assert_response 200, @response.body.to_s
    assert_equal ['2016-07-23', '2016-07-24'], json_body['checked_dates']

    post :check, params: { user_id: absences(:one).user_id,
                           dates: "2016-07-20,2016-07-21,2016-07-22" }, format: :json
    assert_response 200, @response.body.to_s
    assert_equal [], json_body['checked_dates']

    @request.headers['X-Redmine-Absences-Check-API-Key'] = '12345'
    post :check, params: { user_id: absences(:one).user_id,
                           dates: "2016-07-20,2016-07-21,2016-07-22,2016-07-23,2016-07-24" },
                 format: :json
    assert_response 401, @response.body.to_s
    assert_nil json_body['checked_dates']
  end

  def test_check_absence_without_api_key
    @request.headers['X-Redmine-Absences-Check-API-Key'] = nil
    post :check, params: { dates: '2016-07-20,2016-07-21,2016-07-22,2016-07-23,2016-07-24' },
                 format: :json
    assert_response 401, @response.body.to_s
  end

  def test_checked_absences_not_found_and_array_not_modified
    post :check, params: { user_id: absences(:one).user_id,
                           dates: "2016-07-11,2016-07-12,2016-07-13,2016-07-23,2016-07-24" },
                 format: :json
    assert_response 200, @response.body.to_s
    assert_equal ['2016-07-11','2016-07-12','2016-07-13', '2016-07-23', '2016-07-24'], json_body['checked_dates']

    post :check, params: { user_id: 1111,
                           dates: "2016-07-11,2016-07-12,2016-07-13,2016-07-23,2016-07-24" },
                 format: :json
    assert_response 200, @response.body.to_s
    assert_equal ['2016-07-11','2016-07-12','2016-07-13', '2016-07-23', '2016-07-24'], json_body['checked_dates']

    post :check, params: { user_id: 1111,
                           dates: "2016-07-11,2016-07-12,2016-07-13,2016-07-23,2016-07-24" },
                 format: :json
    assert_response 200, @response.body.to_s
    assert_equal ['2016-07-11','2016-07-12','2016-07-13', '2016-07-23', '2016-07-24'], json_body['checked_dates']
  end
end
