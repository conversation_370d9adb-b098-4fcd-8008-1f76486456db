require 'test_helper'

# debug sql:
# tail -n 20000 -f log/test.log | grep -A40 ": test_create_global_role_ids"
class Api::V1::RolesControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :global_roles, :user_global_roles, :users

  def role
    @role ||= roles(:mkalita_role)
    # http://chriskottom.com/blog/2014/10/4-fantastic-ways-to-set-up-state-in-minitest/
    # HOWTO: test specific version
    # @request.headers['Accept'] = 'application/vnd.api+json; version=2'
    # @request.headers['Content-Type'] = 'application/vnd.api+json; version=2' # for sending data via POST etc.
    # or
    # post '/humans',
    #      { human: { name: 'John', brain_type: 'small' } }.to_json,
    #      { 'Accept' => 'application/vnd.api+json; version=2',
    #        'Content-Type' => 'application/vnd.api+json; version=2' }
  end

  def test_rescues_module_inclusion
    assert_includes Api::V1::RolesController.ancestors, Api::V1::Concerns::Handlers::Rescues
  end

  def test_index
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:roles).size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_page_2
    user_on_first_page = Role.order('id DESC').first!
    get :index, format: :json, params: { per_page: 1, page: 2 }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['id'].to_i > 0
    assert json_body.last['id'].to_i != user_on_first_page.id
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_param_collection_for_select
    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:roles).size
    assert !json_body.include?('redmine_id')
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_with_created_after_filter
    get :index, format: :json, params: { f: { created_after: Time.zone.tomorrow } }

    assert_response :success
    assert_equal 0, assigns(:roles).count
  end

  def test_index_with_created_before_filter
    get :index, format: :json, params: { f: { created_before: Time.zone.yesterday } }

    assert_response :success
    assert_equal 0, assigns(:roles).count
  end

  def test_index_with_sort_filter
    get :index, format: :json, params: { f: { sort: 'name' } }

    assert_equal Role.order('name ASC').limit(10).pluck(:id), assigns(:roles).pluck(:id)
  end

  def test_index_with_term_filter
    get :index, format: :json, params: { f: { term: 'Project Manager' } }

    assert_response :success
    assert_equal 2, assigns(:roles).count
    assert_equal [roles(:pm).id, roles(:uber_project_manager).id].sort,
                 json_body.map { |role| role['id'] }.sort
  end

  def test_create
    name = role.name + '_test_create'
    assert_difference('Role.count') do
      post :create, params: { role: {
        name: name,
        ldap: true
      } }, format: :json
      assert_response 201, @response.body.to_s
    end
    role = Role.find(json_body['id'])
    assert_equal role_url(role), response.location
    assert_equal true, role.ldap
    assert_equal name, role.name
  end

  def test_show
    get :show, params: { id: role }, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'url'
  end

  def test_update
    put :update, params: { id: role, role: {
      name: role.name,
      ldap: true
    } }, format: :json
    assert_response 204, @response.body.to_s
    assert true, role.reload.ldap
  end

  def test_update_docs_cloud
    put :update, params: { id: role, role: { name: role.name, docs_ldap: true } }, format: :json
    assert_response 204
    assert true, role.reload.docs_ldap
  end

  def test_update_chat_ldap
    put :update, params: { id: role, role: { name: role.name, chat_ldap: true } }, format: :json
    assert_response 204
    assert true, role.reload.chat_ldap
  end

  def test_destroy
    assert_difference('Role.count', -1) do
      delete :destroy, format: :json, params: { id: roles(:unused_role).id }
      assert_response 204, @response.body.to_s
    end
  end

  def test_destroy_if_associated_records_exist
    assert_difference('Role.count', 0) do
      delete :destroy, format: :json, params: { id: role }
      assert_response 422, @response.body.to_s
      assert_includes json_body, 'errors'
      assert_includes json_body['errors'], 'base'
      assert_kind_of Array, json_body['errors']['base']
      assert_includes json_body['errors']['base'], 'Cannot delete record because dependent membership roles exist'
    end
  end

  def test_index_authorization
    user = users(:mkalita_user)
    policy = stub(index?: false)
    RolePolicy.stubs(:new).with(user, Role).returns(policy)
    sign_in(user)
    get :index, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_show_authorization
    user = users(:mkalita_user)
    role = roles(:mkalita_role)
    policy = stub(show?: false)
    RolePolicy.stubs(:new).with(user, role).returns(policy)
    sign_in(user)
    get :show, params: { id: role.id }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_create_authorization
    user = users(:mkalita_user)
    policy = stub(create?: false)
    RolePolicy.stubs(:new).with(user, Role).returns(policy)
    sign_in(user)
    post :create, params: { role: { name: "Rola1" } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_update_authorization
    user = users(:mkalita_user)
    role = roles(:mkalita_role)
    policy = stub(update?: false)
    RolePolicy.stubs(:new).with(user, role).returns(policy)
    sign_in(user)
    patch :update, params: { id: role.id, role: { name: "Dev1" } }, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_destroy_authorization
    user = users(:mkalita_user)
    role = roles(:mkalita_role)
    policy = stub(destroy?: false)
    RolePolicy.stubs(:new).with(user, role).returns(policy)
    sign_in(user)
    delete :destroy, params: { id: role.id }, format: :json
    assert_response 403, @response.body.to_s
  end
end
