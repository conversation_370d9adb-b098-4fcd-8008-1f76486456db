require 'test_helper'

class Api::V1::NotificationsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  fixtures :holiday_requests, :users, :notifications

  def notification
    @notification ||= notifications(:mkalita_notification)
    # http://chriskottom.com/blog/2014/10/4-fantastic-ways-to-set-up-state-in-minitest/
    # HOWTO: test specific version
    # @request.headers['Accept'] = 'application/vnd.api+json; version=2'
    # @request.headers['Content-Type'] = 'application/vnd.api+json; version=2' # for sending data via POST etc.
    # or
    # post '/humans',
    #      { human: { name: '<PERSON>', brain_type: 'small' } }.to_json,
    #      { 'Accept' => 'application/vnd.api+json; version=2',
    #        'Content-Type' => 'application/vnd.api+json; version=2' }
  end

  def test_rescues_module_inclusion
    assert_includes Api::V1::NotificationsController.ancestors, Api::V1::Concerns::Handlers::Rescues
  end

  def test_index_with_param_collection_for_select
    @request.headers['X-Swagger-Sign-In-As'] = notification.user.id.to_s
    get :index, format: :json, params: { f: { collection_for_select: true } }
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:notifications).size
    assert !json_body.include?('created_at')
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index
    @request.headers['X-Swagger-Sign-In-As'] = notification.user.id.to_s
    get :index, format: :json
    assert_response :success, @response.body.to_s
    assert_equal json_body.size, assigns(:notifications).size
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_index_page_2
    notification.user.notifications.update_all(state: Notification.states['sent'])
    @request.headers['X-Swagger-Sign-In-As'] = notification.user.id.to_s
    record_on_first_page = notification.user.notifications.order('id DESC').first!
    get :index, format: :json, params: { per_page: 1, page: 2 }
    assert_response :success, @response.body.to_s
    refute json_body.empty?
    assert json_body.last['id'].to_i > 0
    refute_equal record_on_first_page.id, json_body.last['id'].to_i
    assert_equal 'application/vnd.api+json; version=1; charset=utf-8', response.header['Content-Type']
  end

  def test_show
    @request.headers['X-Swagger-Sign-In-As'] = notification.user.id.to_s
    notification.update_column(:state, Notification.states['sent'])
    get :show, params: { id: notification.id }, format: :json
    assert_response :success, @response.body.to_s
    assert_includes json_body, 'url'
  end

  def test_index_authorization
    user = users(:mkalita_user)
    policy = stub(index?: false)
    NotificationPolicy.stubs(:new).with(user, Notification).returns(policy)
    sign_in(user)
    get :index, format: :json
    assert_response 403, @response.body.to_s
  end

  def test_show_authorization
    user = users(:mkalita_user)
    notification = notifications(:one)
    notification.update_column(:state, Notification.states['sent'])
    policy = stub(show?: false)
    NotificationPolicy.stubs(:new).with(user, notification).returns(policy)
    sign_in(user)
    get :show, params: { id: notification.id }, format: :json
    assert_response 403, @response.body.to_s
  end
end
