require 'test_helper'

module Api
  module V1
    module Assets
      class VpnsControllerTest < ActionController::TestCase
        describe Api::V1::Assets::VpnsController do
          setup do
            @user = users(:mikolaj)
            @project = projects(:one)
            authenticate(@user)

            @valid_params = {
              project_id: @project.id,
              user_id: @user.id,
              phone: '244224241',
              monitoring: true
            }
          end

          describe 'GET #show' do
            test 'returns asset' do
              @asset = Vpn.create!(project: @project, user: @user, requester: @user,
                                   requested_date: Time.zone.now, notes: 'My awesome notes',
                                   phone: '666777444', dev_server: true, hq_aisvn_services: true)

              post :show, params: { id: @asset.id }, format: :json
              assert_equal json_body['id'], @asset.id
              assert_response :ok
            end
          end

          describe 'POST #create' do
            test 'client is created given valid params' do
              assert_difference('Vpn.count') do
                post :create, params: { vpn: @valid_params }, format: :json
              end

              assert_response :created
            end

            test 'user can have only one not rejected Vpn' do
              assert_difference('Vpn.count', 1) do
                post :create, params: { vpn: @valid_params }, format: :json
              end

              assert_response :created

              assert_difference('Vpn.count', 0) do
                post :create, params: { vpn: @valid_params }, format: :json
              end

              assert_response :unprocessable_entity
              assert_not_empty json_body['errors']
            end

            test 'user can have only one not rejected Vpn - pending' do
              Vpn.create!(project_id: @project.id, user_id: @user.id, requester_id: @user.id, requested_date: Time.current,
                          phone: '244224241', monitoring: true, state: 0)

              assert_difference('Vpn.count', 0) do
                post :create, params: { vpn: @valid_params }, format: :json
              end

              assert_response :unprocessable_entity
              assert_not_empty json_body['errors']
            end

            test 'user can have only one not rejected Vpn - in_progress' do
              Vpn.create!(project_id: @project.id, user_id: @user.id, requester_id: @user.id, requested_date: Time.current,
                          phone: '244224241', monitoring: true, state: 4)

              assert_difference('Vpn.count', 0) do
                post :create, params: { vpn: @valid_params }, format: :json
              end

              assert_response :unprocessable_entity
              assert_not_empty json_body['errors']
            end

            test 'user can have only one not rejected Vpn - active' do
              Vpn.create!(project_id: @project.id, user_id: @user.id, requester_id: @user.id, requested_date: Time.current,
                          phone: '244224241', monitoring: true, state: 1)

              assert_difference('Vpn.count', 0) do
                post :create, params: { vpn: @valid_params }, format: :json
              end

              assert_response :unprocessable_entity
              assert_not_empty json_body['errors']
            end

            test 'user can have only one requested Vpn - when rest of them are closed' do
              Vpn.create!(project_id: @project.id, user_id: @user.id, requester_id: @user.id, requested_date: Time.current,
                          phone: '244224241', monitoring: true, state: 2)

              assert_difference('Vpn.count', 1) do
                post :create, params: { vpn: @valid_params }, format: :json
              end

              assert_response :created
            end

            test 'user can have only one requested Vpn - when rest of them are rejected' do
              Vpn.create!(project_id: @project.id, user_id: @user.id, requester_id: @user.id, requested_date: Time.current,
                          phone: '244224241', monitoring: true, state: 3)

              assert_difference('Vpn.count', 1) do
                post :create, params: { vpn: @valid_params }, format: :json
              end

              assert_response :created
            end

            test 'user can have only one requested Vpn - when rest of them are decommission_pending' do
              Vpn.create!(project_id: @project.id, user_id: @user.id, requester_id: @user.id, requested_date: Time.current,
                          phone: '244224241', monitoring: true, state: 5)

              assert_difference('Vpn.count', 1) do
                post :create, params: { vpn: @valid_params }, format: :json
              end

              assert_response :created
            end

            it 'succeed with project' do
              assert_difference('Vpn.count', 1) do
                post :create, params: { vpn: @valid_params.merge(user_id: nil) }, format: :json
              end

              assert_response :created
            end

            it 'succeed with user' do
              assert_difference('Vpn.count', 1) do
                post :create, params: { vpn: @valid_params.merge(project_id: nil) }, format: :json
              end

              assert_response :created
            end

            it 'succeed with project and user' do
              assert_difference('Vpn.count', 1) do
                post :create, params: { vpn: @valid_params }, format: :json
              end

              assert_response :created
            end

            it 'fails without project and user' do
              assert_difference('Vpn.count', 0) do
                post :create, params: { vpn: @valid_params.merge(project_id: nil, user_id: nil) }, format: :json
              end

              assert_response :unprocessable_entity
            end
          end
        end
      end
    end
  end
end
