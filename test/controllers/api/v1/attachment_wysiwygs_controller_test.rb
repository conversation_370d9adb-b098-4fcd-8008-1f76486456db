require 'test_helper'
require "minitest/autorun"

class Api::V1::AttachmentWysiwygsControllerTest < ActionController::TestCase
  include Minitest::XSwaggerSignInAs

  test 'POST #create' do
    @request.headers['X-Swagger-Sign-In-As'] = users(:capybara_global_admin_user).id.to_s
    @attachment_count = AttachmentWysiwyg.count
    post :create, params: {
      file: Rack::Test::UploadedFile.new(Rails.root.join('test/fixtures/files/jpg_705kB.jpg'),
                                         'image/jpg')
    }
    assert_response :created, @response.body.to_s
    assert_equal @attachment_count + 1 , AttachmentWysiwyg.count
    attachment = AttachmentWysiwyg.last
    assert_equal attachment.file_url, json_body['location']
    assert_equal attachment.id, json_body['id']
  end
end
