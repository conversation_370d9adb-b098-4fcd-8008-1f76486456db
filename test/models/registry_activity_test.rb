require "test_helper"

describe RegistryActivity do
  let(:user) { users(:mkalita_user) }

  let(:valid_attributes) do
    {
      description: "Test registry activity",
      activity: "Processing personal data for marketing purposes",
      administrator_type: :artegence,
      co_administrator_type: :efigence,
      data_processing_goals: "Marketing and customer analysis",
      legal_basis: "Consent of the data subject",
      subjects_categories: "Customers and website visitors",
      personal_data_categories: "Name, email, phone number",
      categories_of_recipients: "Marketing department, external marketing agencies",
      transfer_to_third_country: "No transfer to third countries",
      planned_deletion_dates: "3 years after last contact",
      security_measures: "Encryption, access control, regular security audits",
      creation_date: Date.today.to_s,
      created_by: user
    }
  end

  should belong_to(:created_by).class_name('User').optional
  should have_many(:risk_analyses)

  should validate_presence_of(:description)
  should validate_presence_of(:activity)
  should validate_presence_of(:administrator_type)
  should validate_presence_of(:co_administrator_type)
  should validate_presence_of(:data_processing_goals)
  should validate_presence_of(:legal_basis)
  should validate_presence_of(:subjects_categories)
  should validate_presence_of(:personal_data_categories)
  should validate_presence_of(:categories_of_recipients)
  should validate_presence_of(:transfer_to_third_country)
  should validate_presence_of(:planned_deletion_dates)
  should validate_presence_of(:security_measures)
  should validate_presence_of(:creation_date)
  should validate_presence_of(:expiration_date).allow_nil

  describe "validations" do
    it "is valid with valid attributes" do
      registry_activity = RegistryActivity.new(valid_attributes)
      assert registry_activity.valid?
    end

    it "requires administrator_type_custom when administrator_type is 'other'" do
      registry_activity = RegistryActivity.new(valid_attributes.merge(
        administrator_type: :other,
        administrator_type_custom: nil
      ))
      assert registry_activity.invalid?
      assert_includes registry_activity.errors[:administrator_type_custom], "can't be blank"
    end

    it "requires co_administrator_type_custom when co_administrator_type is 'other'" do
      registry_activity = RegistryActivity.new(valid_attributes.merge(
        co_administrator_type: :other,
        co_administrator_type_custom: nil
      ))
      assert registry_activity.invalid?
      assert_includes registry_activity.errors[:co_administrator_type_custom], "can't be blank"
    end

    it "does not require administrator_type_custom when administrator_type is not 'other'" do
      registry_activity = RegistryActivity.new(valid_attributes.merge(
        administrator_type: :artegence,
        administrator_type_custom: nil
      ))
      assert registry_activity.valid?
    end

    it "does not require co_administrator_type_custom when co_administrator_type is not 'other'" do
      registry_activity = RegistryActivity.new(valid_attributes.merge(
        co_administrator_type: :efigence,
        co_administrator_type_custom: nil
      ))
      assert registry_activity.valid?
    end

    it "is invalid with an invalid expiration_date format" do
      registry_activity = RegistryActivity.new(valid_attributes.merge(expiration_date: "not-a-date"))
      assert registry_activity.invalid?
      assert_not_empty registry_activity.errors[:expiration_date]
    end
  end

  describe "enums" do
    it "defines administrator_type enum" do
      assert_equal({ "other" => 0, "artegence" => 1, "efigence" => 2 }, RegistryActivity.administrator_types)
    end

    it "defines co_administrator_type enum" do
      assert_equal({ "other" => 0, "artegence" => 1, "efigence" => 2 }, RegistryActivity.co_administrator_types)
    end

    it "defines state enum" do
      assert_equal({ "active" => 0, "closed" => 1 }, RegistryActivity.states)
    end
  end

  describe "#administrator_type_name" do
    it "returns administrator_type_custom when administrator_type is 'other'" do
      registry_activity = RegistryActivity.new(valid_attributes.merge(
        administrator_type: :other,
        administrator_type_custom: "Custom Administrator"
      ))
      assert_equal "Custom Administrator", registry_activity.administrator_type_name
    end

    it "returns capitalized administrator_type when administrator_type is not 'other'" do
      registry_activity = RegistryActivity.new(valid_attributes.merge(administrator_type: :artegence))
      assert_equal "Artegence", registry_activity.administrator_type_name
    end
  end

  describe "#co_administrator_type_name" do
    it "returns co_administrator_type_custom when co_administrator_type is 'other'" do
      registry_activity = RegistryActivity.new(valid_attributes.merge(
        co_administrator_type: :other,
        co_administrator_type_custom: "Custom Co-Administrator"
      ))
      assert_equal "Custom Co-Administrator", registry_activity.co_administrator_type_name
    end

    it "returns capitalized co_administrator_type when co_administrator_type is not 'other'" do
      registry_activity = RegistryActivity.new(valid_attributes.merge(co_administrator_type: :efigence))
      assert_equal "Efigence", registry_activity.co_administrator_type_name
    end
  end

  describe "state machine" do
    it "has active as the initial state" do
      registry_activity = RegistryActivity.new(valid_attributes)
      assert registry_activity.active?
      assert_equal "active", registry_activity.state
    end

    describe "activate event" do
      it "transitions from closed to active" do
        registry_activity = registry_activities(:two)
        assert registry_activity.closed?

        registry_activity.activate!
        assert registry_activity.active?
      end

      it "raises an error when trying to activate an already active registry activity" do
        registry_activity = registry_activities(:one)
        assert registry_activity.active?

        assert_raises(AASM::InvalidTransition) do
          registry_activity.activate!
        end
      end
    end

    describe "close event" do
      it "transitions from active to closed" do
        registry_activity = registry_activities(:one)
        assert registry_activity.active?

        registry_activity.close!
        assert registry_activity.closed?
      end

      it "raises an error when trying to close an already closed registry activity" do
        registry_activity = registry_activities(:two)
        assert registry_activity.closed?

        assert_raises(AASM::InvalidTransition) do
          registry_activity.close!
        end
      end
    end

    describe "state persistence" do
      it "persists state changes to the database" do
        registry_activity = registry_activities(:one)
        assert registry_activity.active?

        registry_activity.close!
        registry_activity.reload
        assert registry_activity.closed?

        registry_activity.activate!
        registry_activity.reload
        assert registry_activity.active?
      end
    end

    describe "scopes" do
      it "has an active scope that returns only active registry activities" do
        active_activities = RegistryActivity.active
        assert_includes active_activities, registry_activities(:one)
        assert_not_includes active_activities, registry_activities(:two)
      end

      it "has a closed scope that returns only closed registry activities" do
        closed_activities = RegistryActivity.closed
        assert_includes closed_activities, registry_activities(:two)
        assert_not_includes closed_activities, registry_activities(:one)
      end
    end

    describe "edge cases" do
      it "allows creating a registry activity with a specific state" do
        registry_activity = RegistryActivity.new(valid_attributes.merge(state: :closed))
        assert registry_activity.closed?
        assert_equal "closed", registry_activity.state
      end

      it "allows updating the state directly" do
        registry_activity = registry_activities(:one)
        registry_activity.update(state: "closed")
        assert registry_activity.closed?
      end
    end
  end
end
