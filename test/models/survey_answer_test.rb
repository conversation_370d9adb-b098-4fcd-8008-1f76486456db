require "test_helper"

class SurveyAnswerTest < ActiveSupport::TestCase
  test 'creates question answers on create' do
    survey = surveys(:onboarding)
    survey_question = survey_questions(:onboarding)
    evaluation = evaluations(:milosz_evaluation)
    user = users(:wik<PERSON>)
    evaluated_user = users(:milosz)
    survey_answer = SurveyAnswer.create!(survey: survey, user: user, evaluation: evaluation)
    refute_empty survey_answer.reload.question_answers
    assert_equal survey_question.content, survey_answer.question_answers.first.question_content
  end

  test 'question answer errors should be indexed' do
    survey_answer = survey_answers(:onboarding_chief_answer)
    question_answer = question_answers(:onboarding_chief_question_answer)
    params = {
      state: 'completed',
      question_answers_attributes: [{ id: question_answer.id, content: nil }]
    }
    refute survey_answer.update(params)
    refute_empty survey_answer.errors.messages[:'question_answers[0].content']
  end
end
