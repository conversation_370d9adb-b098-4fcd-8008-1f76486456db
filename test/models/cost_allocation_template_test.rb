require 'test_helper'

describe CostAllocationTemplate do
  subject { cost_allocation_templates(:wiktoria_template) }

  should belong_to(:user)
  should have_many(:cost_allocation_template_positions).dependent(:destroy)

  should validate_presence_of(:name)

  it 'should validate positions share amounts consistency' do
    position1, position2 = subject.cost_allocation_template_positions
    position1.share_amount = 70
    position2.share_amount = 50

    assert subject.invalid?
    assert subject.errors.added?(:total_positions_share_amount, :is_not_equal_to_one)

    position2.share_amount = 30
    assert subject.valid?
  end

  should accept_nested_attributes_for(:cost_allocation_template_positions).allow_destroy(true)
end
