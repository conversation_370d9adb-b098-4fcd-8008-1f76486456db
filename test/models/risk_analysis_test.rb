require "test_helper"

describe RiskAnalysis do
  let(:valid_attributes) do
    {
      name: "Test Risk Analysis",
      company: companies(:one),
      registry_activity: registry_activities(:one),
      creation_date: Date.today.to_s,
      property: :confidentiality,
      danger: "Unauthorized access to personal data",
      vulnerability_description: "Weak password policies and lack of two-factor authentication",
      security: "Implement strong password policies and enable two-factor authentication",
      probability: :possible,
      effect: :high,
      data_processing_impact_assessment: :required
    }
  end

  should belong_to(:company)
  should belong_to(:registry_activity).optional

  should validate_presence_of(:name)
  should validate_presence_of(:company_id)
  should validate_presence_of(:registry_activity_id)
  should validate_presence_of(:creation_date)
  should validate_presence_of(:property)
  should validate_presence_of(:danger)
  should validate_presence_of(:security)
  should validate_presence_of(:probability)
  should validate_presence_of(:vulnerability_description)
  should validate_presence_of(:effect)
  should validate_presence_of(:data_processing_impact_assessment)

  describe "validations" do
    it "is valid with valid attributes" do
      risk_analysis = RiskAnalysis.new(valid_attributes)
      assert risk_analysis.valid?
    end

    %i[name company registry_activity_id creation_date property danger vulnerability_description security probability
    effect data_processing_impact_assessment].each do |attribute|
      it "is invalid without #{attribute}" do
        key = attribute.end_with?('_id') ? attribute[0..-4] : attribute
        risk_analysis = RiskAnalysis.new(valid_attributes.except(key&.to_sym))

        assert risk_analysis.invalid?
        assert_not_empty risk_analysis.errors[attribute]
      end
    end
  end

  describe "risk calculation" do
    it "calculates low risk for rare probability and very_low effect" do
      risk_analysis = RiskAnalysis.new(valid_attributes.merge(probability: :rare, effect: :very_low))
      risk_analysis.save!
      assert_equal "low", risk_analysis.risk
    end

    it "calculates medium risk for possible probability and low effect" do
      risk_analysis = RiskAnalysis.new(valid_attributes.merge(probability: :possible, effect: :low))
      risk_analysis.save!
      assert_equal "medium", risk_analysis.risk
    end

    it "calculates high risk for probable probability and medium effect" do
      risk_analysis = RiskAnalysis.new(valid_attributes.merge(probability: :probable, effect: :medium))
      risk_analysis.save!
      assert_equal "high", risk_analysis.risk
    end

    it "calculates critical risk for almost_certain probability and very_high effect" do
      risk_analysis = RiskAnalysis.new(valid_attributes.merge(probability: :almost_certain, effect: :very_high))
      risk_analysis.save!
      assert_equal "critical", risk_analysis.risk
    end

    it "recalculates risk when probability changes" do
      risk_analysis = RiskAnalysis.create!(valid_attributes.merge(probability: :rare, effect: :very_low))
      assert_equal "low", risk_analysis.risk

      risk_analysis.update!(probability: :almost_certain)
      assert_equal "medium", risk_analysis.risk
    end

    it "recalculates risk when effect changes" do
      risk_analysis = RiskAnalysis.create!(valid_attributes.merge(probability: :possible, effect: :low))
      assert_equal "medium", risk_analysis.risk

      risk_analysis.update!(effect: :very_high)
      assert_equal "critical", risk_analysis.risk
    end
  end
end
