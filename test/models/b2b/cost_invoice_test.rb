require 'test_helper'

module B2B
  class CostInvoiceTest < ActiveSupport::TestCase
    let(:user) { users(:wik<PERSON>) }
    let(:project) { projects(:two) }
    let(:cost_invoice) { cost_invoices(:wiktoria_cost_invoice) }
    let(:click_up_response) { {
      data: [
        {
          id: 1, user: { id: user.click_up_id, email: user.email },
          duration: 20000000
        },
        {
          id: 2, user: { id: user.click_up_id, email: user.email },
          duration: 8800000
        }
      ]
    } }

    before do
      response = {
        time_entries: [
          {
            id: 1, project: { id: project.redmine_id, identifier: project.identifier },
            user: { login: user.username },
            hours: 96
          },
          {
            id: 2, project: { id: project.redmine_id, identifier: project.identifier },
            user: { login: user.username },
            hours: 56
          }
        ],
        total_count: 2
      }
      stub_request(:get, "#{Settings.redmine_working_time.base_url}/timeapi.json")
        .with(query: hash_including('limit' => '1000'))
        .to_return(body: response.to_json, headers: { 'Content-Type' => 'application/json' })
      stub_request(:get, "#{Settings.click_up_api.uri}/team/#{companies(:one).click_up_workspace_id}/time_entries")
        .with(
          query: {
            assignee: user.click_up_id,
            start_date: cost_invoice.sell_date.beginning_of_month.to_datetime.to_i * 1000,
            end_date: cost_invoice.sell_date.end_of_month.to_datetime.to_i * 1000
          }
        )
        .to_return(body: click_up_response.to_json, headers: { 'Content-Type' => 'application/json' })
      stub_request(:get, "#{Settings.click_up_api.uri}/team/#{companies(:one).click_up_workspace_id}/time_entries")
        .with(
          query: {
            assignee: user.click_up_id,
            start_date: cost_invoices(:wiktoria_draft_cost_invoice).sell_date.beginning_of_month.to_datetime.to_i * 1000,
            end_date: cost_invoices(:wiktoria_draft_cost_invoice).sell_date.end_of_month.to_datetime.to_i * 1000
          }
        )
        .to_return(body: click_up_response.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    should belong_to(:contractor)

    test 'number uniqueness is validated' do
      stub_request(:get, "#{Settings.click_up_api.uri}/team/#{companies(:one).click_up_workspace_id}/time_entries")
        .with(
          query: {
            assignee: user.click_up_id,
            start_date: 2.days.ago.to_date.beginning_of_month.to_datetime.to_i * 1000,
            end_date: 2.days.ago.to_date.end_of_month.to_datetime.to_i * 1000
          }
        )
        .to_return(body: click_up_response.to_json, headers: { 'Content-Type' => 'application/json' })

      cost_invoice = B2B::CostInvoice.new(
        title: 'Wiktoria invoice title',
        user: users(:wiktoria),
        number: '2020/02/1',
        description: 'description',
        company: companies(:one),
        document_data: TestData.document_data,
        sell_date: 2.days.ago,
        due_date: 2.weeks.from_now.to_date,
        invoice_date: 2.days.ago,
        contractor: contractors(:wiktoria_contractor),
        cost_invoice_positions_attributes: [{
          name: 'Position name',
          amount: 1,
          unit_price: 1000,
          tax_rate: :'0'
        }]
      )
      assert cost_invoice.valid?

      cost_invoice.send_to_controller
      assert cost_invoice.invalid?
      assert_equal ['an invoice with the given number and contractor already exists in the system'], cost_invoice.errors[:number]
    end

    test 'contractor is validated' do
      cost_invoice.contractor = nil

      assert cost_invoice.invalid?
      assert_not_empty cost_invoice.errors[:contractor]
    end

    test 'contractor user presence is validated' do
      cost_invoice.contractor = contractors(:non_user_contractor)

      assert cost_invoice.invalid?
      assert_not_empty cost_invoice.errors[:contractor]
    end

    test 'sell_date presence is validated' do
      cost_invoice.sell_date = nil

      assert cost_invoice.invalid?
      assert_not_empty cost_invoice.errors[:sell_date]
    end

    test 'due_date presence is validated' do
      cost_invoice.due_date = nil

      assert cost_invoice.invalid?
      assert_not_empty cost_invoice.errors[:due_date]
    end

    test 'invoice_date presence is validated' do
      cost_invoice.invoice_date = nil

      assert cost_invoice.invalid?
      assert_not_empty cost_invoice.errors[:invoice_date]
    end

    test 'invoice date not in the future is validated' do
      cost_invoice.invoice_date = Date.tomorrow

      assert cost_invoice.invalid?
      assert_equal ["must be before or equal to #{Date.current}"], cost_invoice.errors[:invoice_date]
    end

    test 'company presence is validated' do
      cost_invoice.company = nil

      assert cost_invoice.invalid?
      assert_not_empty cost_invoice.errors[:company]
    end

    test 'document presence is validated' do
      cost_invoice.document = nil

      assert cost_invoice.invalid?
      assert_not_empty cost_invoice.errors[:document]
    end

    test 'cost_projects save properly' do
      cost_invoice.save

      cost_project = cost_invoice.cost_projects.first
      assert_equal accounting_numbers(:one), cost_project.accounting_number
      assert_equal cost_invoice.total_amount, cost_project.amount
    end

    test 'document format is validated' do
      cost_invoice = cost_invoices(:wiktoria_cost_invoice)
      cost_invoice.document = File.open('test/fixtures/files/mp4.mp4')

      assert cost_invoice.invalid?
      assert_not_empty cost_invoice.errors[:document]
    end

    test 'send_to_controller moves cost invoice to pending_department state' do
      cost_invoice = cost_invoices(:wiktoria_draft_cost_invoice)

      cost_invoice.send_to_controller!

      assert cost_invoice.reload.pending_department?
    end

    test "send_to_controller moves board member's invoice to pending state" do
      cost_invoice = cost_invoices(:wiktoria_draft_cost_invoice)
      cost_invoice.contractor.user.department.update(board_member: true)

      cost_invoice.send_to_controller!

      assert cost_invoice.reload.pending?
    end

    test 'accept moves cost invoice from pending_department to pending state' do
      cost_invoice = cost_invoices(:wiktoria_draft_cost_invoice)
      cost_invoice.send_to_controller!

      cost_invoice.accept!

      assert cost_invoice.reload.pending?
    end

    test 'accept causes auto-generation of cost-projects when needed' do
      cost_invoice = cost_invoices(:wiktoria_draft_cost_invoice)
      cost_projects_attributes = [{
        accounting_number_id: accounting_numbers(:one).id, amount: 2000
      }]
      RedmineWorkingTimeApi::CostProjectsBuilder.stubs(:generate)
                                                .with(cost_invoice.total_amount,
                                                      cost_invoice.contractor.user,
                                                      cost_invoice.sell_date)
                                                .returns(cost_projects_attributes)
      cost_invoice.force_accept = true
      cost_invoice.cost_projects.destroy_all
      cost_invoice.auto_cost_projects = true
      cost_invoice.send_to_controller!
      cost_invoice.accept! # pending_department to pending transition

      cost_invoice.accept!

      assert_equal 1, cost_invoice.cost_projects.reload.count
      assert_equal 2000, cost_invoice.cost_projects.first.amount
    end

    test 'accepted_at is populated upon acceptation' do
      cost_invoice.accept!

      assert cost_invoice.accepted?
      assert cost_invoice.accepted_at.present?
    end

    test 'withdraw marks an invoices as for_correction' do
      cost_invoice.withdraw!

      assert cost_invoice.reload.for_correction?
    end

    test 'recall moves the state from accepted to pending_controller if the date is right' do
      cost_invoice.auto_cost_projects = false
      cost_invoice.enable_force_accept
      cost_invoice.accounting_number_id = accounting_numbers(:one)
      cost_invoice.accept!
      cost_invoice.accepted_at = 1.day.ago
      cost_invoice.sent = true

      assert_not cost_invoice.may_recall?

      cost_invoice.accepted_at = Time.zone.now

      assert cost_invoice.recall
      assert cost_invoice.pending?
      assert_nil cost_invoice.accepted_at
      assert_not cost_invoice.sent?
    end

    test 'create_snapshot creates snapshot with snapshot items' do
      user = users(:wiktoria)
      state = cost_invoice.state
      action = :action
      comment = 'comment'

      assert_difference -> { cost_invoice.snapshots.count }, 1 do
        cost_invoice.create_snapshot(user, action, comment: comment)
      end

      snapshot = cost_invoice.snapshots.last
      assert_equal cost_invoice, snapshot.item
      assert_equal user, snapshot.user
      assert_equal ({ action: action, comment: comment, state_was: state }), snapshot.metadata

      assert_equal 3, snapshot.snapshot_items.count
      cost_invoice_item, _ = snapshot.fetch_reified_items
      assert_equal cost_invoice, cost_invoice_item
    end

    test 'create_snapshot creates snapshot children' do
      cost_invoice.create_snapshot(users(:wiktoria), :action, comment: 'comment')

      snapshot = cost_invoice.snapshots.last
      _, children_hash = snapshot.fetch_reified_items
      assert_equal cost_invoice.cost_invoice_positions, children_hash[:cost_invoice_positions]
      assert_equal cost_invoice.cost_projects, children_hash[:cost_projects]
    end
  end
end
