require 'test_helper'

class ServerTest < ActiveSupport::TestCase
  should validate_presence_of(:requested_date)
  should belong_to(:project)
  should belong_to(:requester)
  should belong_to(:activated_by).optional

  test 'admin_request_content contains sender/receiver address(es)' do
    server = servers(:server_one)

    request_content = server.admin_request_content

    assert_match server.receivers_addresses, request_content
  end

  test 'notifies admin on successful execution' do
    server = servers(:server_one)
    user = users(:wiktoria)

    assert_difference('AdminRequestMailer.deliveries.count', 1) do
      server.execute!(user)
    end
    assert_equal 'in_progress', server.state
  end

  test 'notifies admin on successful closing' do
    server = servers(:server_one)
    user = users(:wiktoria)
    server.activate!(user)

    assert_difference('AdminRequestMailer.deliveries.count', 1) do
      server.close!(user, 'decommission comment')
    end
    assert_equal 'decommission_pending', server.state
  end

  test 'notifies PM on successful decommission' do
    server = servers(:server_one)
    user = users(:wik<PERSON>)
    server.assign_attributes(state: :decommission_pending, passed_to_decommission_by: user,
                             decommission_comment: 'decommission comment')

    assert_difference('AssetMailer.deliveries.count', 1) do
      server.decommission!
    end
    assert_equal 'closed', server.state
  end
end
