require 'test_helper'

class Pages::ProfileTest < ActiveSupport::TestCase
  let(:user) { users(:w<PERSON><PERSON>) }

  subject { Pages::Profile.new(user) }

  test 'cache_key works properly' do
    assert_equal "profiles/#{subject.to_param}-#{subject.updated_at.utc.to_fs(:nsec)}",
                 subject.cache_key
  end

  test 'privileged_user? works properly' do
    assert subject.privileged_user?
    assert_not Pages::Profile.new(users(:milosz)).privileged_user?
  end

  test 'hr_user? works properly' do
    assert subject.hr_user?
    assert_not Pages::Profile.new(users(:milosz)).hr_user?
  end
end
