require 'test_helper'

class RedmineUsersWorkerTest < ActiveSupport::TestCase
  setup do
    @worker = RedmineUsersWorker.new
  end

  test 'creates redmine user and sets redmine_id' do
    VCR.use_cassette('create_redmine_user') do
      user = users(:milosz)
      @worker.perform(:update, user.id)
      assert user.reload.redmine_id.present?
    end
  end

  test 'updates redmine user and unlocks imperator user edition' do
    VCR.use_cassette('update_redmine_user') do
      user = users(:wiktoria)
      user.update_attribute(:last_name, 'Windsor')
      assert @worker.perform(:update, user.id)
    end
  end

  test 'updates redmine users time_reports_not_required' do
    VCR.use_cassette('update_redmine_user') do
      user = users(:wiktoria)
      user.update_attribute(:time_reports_not_required, true)
      assert @worker.perform(:update, user.id)
    end
  end

  test 'locks redmine user' do
    VCR.use_cassette('lock_redmine_user') do
      user = users(:wiktoria)
      result = @worker.perform(:lock, user.redmine_id)
      assert result
    end
  end
end
