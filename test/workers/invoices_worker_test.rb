require 'test_helper'

class InvoicesWorkerTest < ActiveSupport::TestCase
  test 'generates invoice xml file' do
    invoice = invoices(:project_five_second_payment_invoice)

    assert_difference('SMBWorker.jobs.count', 4) do
      subject.perform(invoice.id)
    end
  end

  test 'creates snapshot' do
    invoice = invoices(:project_five_second_payment_invoice)

    assert_difference -> { invoice.snapshots.count }, 1 do
      subject.perform(invoice.id)
    end
  end
end
