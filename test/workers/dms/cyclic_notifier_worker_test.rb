require 'test_helper'

module Dms
  class CyclicNotifierWorkerTest < ActiveSupport::TestCase
    setup do
      Dms::NotificationMailer.deliveries = []
    end

    test 'sends supervisor notification for pending cost invoice not notified for more than 48 h' do
      cost_invoice1 = cost_invoices(:dms_pending_cost_invoice)
      cost_invoice2 = cost_invoices(:dms_pending_cost_invoice_two)

      subject.perform

      assert Dms::NotificationMailer.deliveries.detect do |mail|
        mail.subject =~ 'awaiting review'
      end
      assert Dms::NotificationMailer.deliveries.detect do |mail|
        mail.body.parts.detect { |part| part.to_s =~ cost_invoice1.id }
      end
      assert Dms::NotificationMailer.deliveries.detect do |mail|
        mail.body.parts.detect { |part| part.to_s =~ cost_invoice2.id }
      end
    end

    test 'send notification to department' do
      cost_invoice1 = cost_invoices(:dms_pending_cost_invoice)

      supervisor = users(:milosz)
      department = cost_invoice1.user.department
      uber_chief = department.uber_chief
      department.supervisor = supervisor
      department.save!

      Absence.create({ user_id: uber_chief.id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })

      subject.perform

      assert_includes Dms::NotificationMailer.deliveries.flat_map(&:to), supervisor.email
      assert_includes Dms::NotificationMailer.deliveries.flat_map(&:to), uber_chief.email
    end

    test 'send notification to uber chief' do
      cost_invoice1 = cost_invoices(:dms_pending_cost_invoice)

      cost_invoice1.unaccepted_cost_invoice_acceptances.first.update(kind: 0)

      uber_chief = users(:milosz)
      department1 = cost_invoice1.user.department
      department1.uber_chief = uber_chief
      department1.save!

      Absence.create({ user_id: department1.chief.id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })

      subject.perform

      assert_includes Dms::NotificationMailer.deliveries.flat_map(&:to), uber_chief.email
      assert_includes Dms::NotificationMailer.deliveries.flat_map(&:to), department1.chief.email
    end

    test 'send notification to supervisor' do
      cost_invoice1 = cost_invoices(:dms_pending_cost_invoice)

      cost_invoice1.unaccepted_cost_invoice_acceptances.first.update(kind: 0)

      supervisor = users(:milosz)
      substitute_chief = users(:mikolaj)
      department1 = cost_invoice1.user.department
      department1.substitute_chief = users(:mikolaj)
      department1.supervisor = supervisor
      department1.save!

      chief = department1.chief

      Absence.create({ user_id: department1.chief.id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })
      Absence.create({ user_id: substitute_chief.id, date: Date.today, holiday_request_id: holiday_requests(:one).id, visible: true })

      subject.perform

      assert_includes Dms::NotificationMailer.deliveries.flat_map(&:to), supervisor.email
      assert_includes Dms::NotificationMailer.deliveries.flat_map(&:to), chief.email
    end

    test 'sends author notification for draft cost invoice not notified for more than 48 h' do
      cost_invoice = cost_invoices(:dms_cost_invoice_project)

      subject.perform

      assert Dms::NotificationMailer.deliveries.detect do |mail|
        mail.body.parts.detect { |part| part.to_s =~ cost_invoice.id }
      end
    end
  end
end
