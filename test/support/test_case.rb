require 'support/helpers/test_password_helper'
require 'support/helpers/mailer_test_helper'
require 'support/helpers/stub_employees_report_helper'
require 'support/helpers/stub_redmine_time_import_helper'

class ActionMailer::TestCase
  include ActiveJob::TestHelper
  include MailerTestHelper
end

class ActionDispatch::IntegrationTest
  include Capybara::Screenshot::MiniTestPlugin
end

class ActionController::TestCase
  include Devise::Test::ControllerHelpers
  include ActiveJob::TestHelper

  def authenticate(user)
    request.env['devise.mapping'] = Devise.mappings[:user]
    request.headers.merge!(user.create_new_auth_token)
    sign_in(user)
  end
end

class ActiveSupport::TestCase
  ActiveRecord::Migration.check_pending!
  include TestPasswordHelper
  include StubEmployeesReportHelper
  include StubRedmineTimeImportHelper

  parallelize(workers: :number_of_processors)

  unless ENV['NO_COVERAGE'].to_s == 'true'
    # https://github.com/simplecov-ruby/simplecov/issues/718

    parallelize_setup do |worker|
      SimpleCov.command_name "#{SimpleCov.command_name}-#{worker}"
    end

    parallelize_teardown do |_worker|
      SimpleCov.result
    end
  end

  # Setup all fixtures in test/fixtures/*.yml for all tests in alphabetical order.
  fixtures :all

  def teardown
    super
    Draper::ViewContext.clear!
  end

  def setup
    Settings.accounting = {
      'users_to_notify' => [users(:milosz).id],
      'roles_to_notify' => [roles(:pm).id],
      'invoice_allocation_recipients' => ['<EMAIL>'],
      'remind_after_hours' => 5,
      'repeat_remind_after_days' => 1,
      'invoice_sending_email_from' => { 'Efigence' => '<EMAIL>', 'Artegence' => '<EMAIL>' },
      'invoice_sending_email_bcc' => { 'Efigence' => '<EMAIL>', 'Artegence' => '<EMAIL>' }
    }
  end

  # table name is "morning_appts". It is being mapped to model "MorningAppointment".

  # in this case fixture is namespaced
  # self.set_fixture_class '/legacy/users' => User

  # in this case the model is namespaced.
  # self.set_fixture_class outdoor_games: Legacy::OutdoorGame

  # Add more helper methods to be used by all tests here...
  def json_body
    return '' if @response.body.blank?
    ActiveSupport::JSON.decode(@response.body)
  end

  def generate_valid_password
    default_password
  end
end

def without_conditional_get
  orig = ActionController::Base.allow_forgery_protection
  begin
    ActionController::Base.allow_forgery_protection = true
    yield if block_given?
  ensure
    ActionController::Base.allow_forgery_protection = orig
  end
end

def without_authorization
  headers
  yield if block_given?
end

module Minitest::XSwaggerSignInAs
  def before_setup
    super
    @request.headers['X-Swagger-Sign-In-As'] = users(:mkalita_user).id.to_s
  end
end
