module ForAgreementHelper
  def generate_user_contract(users_array) # rubocop:disable Metrics/AbcSize
    users_array.map do |user_hash|
      user_hash[:user].user_contracts.destroy_all
      user_hash[:user].user_contracts.create!(agreement_type: user_hash[:contract_type],
                                              starts_on: Date.current - 2.months,
                                              ends_on: user_hash[:active] ? Date.current + 1.month : Date.current - 1.month,
                                              month_notice_period: 1)
    end
  end

  def setup_test_users_and_contracts(employment, mandate, b2b, contract_work)
    milosz = users(:milosz)
    mikolaj = users(:mikolaj)
    wiktoria = users(:wiktoria)
    wilhelm = users(:wilhelm)
    wilhelm.update(company: companies(:one))

    generate_user_contract([
                             { user: milosz, contract_type: :employment, active: employment },
                             { user: mikolaj, contract_type: :mandate, active: mandate },
                             { user: wiktoria, contract_type: :b2b, active: b2b },
                             { user: wilhelm, contract_type: :contract_work, active: contract_work }
                           ])
  end

  def create_agreement(contract_types = {})
    defaults = {
      name: 'Agreement',
      content: 'Agreement content',
      confirmation_button_text: 'Agreement confirmation',
      company_ids: [companies(:one).id],
      department_ids: [departments(:one).id, departments(:two).id, departments(:three).id],
      contract_of_employment: false,
      mandate_contract: false,
      business_to_business: false,
      contract_work: false
    }

    Agreement.create!(defaults.merge(contract_types))
  end

  def assert_user_included(agreement, user_fixtures)
    user_fixtures.each do |fixture|
      user = users(fixture)
      assert_includes agreement.reload.users.map(&:full_name), user.full_name
    end
  end

  def assert_users_excluded(agreement, user_fixtures)
    user_fixtures.each do |fixture|
      user = users(fixture)
      assert_not_includes agreement.reload.users.map(&:full_name), user.full_name
    end
  end
end
