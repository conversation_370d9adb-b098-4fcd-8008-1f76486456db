module UsersHelper
  def generate_user(company: companies(:agreement_company),
                    department: departments(:agreement_department),
                    contract_of_employment: false,
                    idx: SecureRandom.hex(10))

    uniq_string = "#{idx}_#{company.id}_#{department.id}_#{contract_of_employment}"

    User.create! do |u|
      u.username = "user_#{uniq_string}"
      u.first_name = 'Agreement'
      u.last_name = "User#{idx}"
      u.email = "agreement_user+#{uniq_string}@artegence.com"
      u.password = u.password_confirmation = 'SDGpoijp23#$'
      u.confirmed_at = Time.zone.now
      u.activates_on = Time.zone.today
      u.company = company
      u.department = department
      u.contract_of_employment = contract_of_employment
    end
  end

  def generate_users(
    count,
    company: companies(:agreement_company),
    department: departments(:agreement_department),
    contract_of_employment: false
  )

    1.upto(count).map do |idx|
      generate_user(company: company,
                    department: department,
                    contract_of_employment: contract_of_employment,
                    idx: idx)
    end
  end
end
