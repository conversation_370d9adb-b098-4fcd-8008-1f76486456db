# Read about fixtures at http://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

one:
  name: "Marketing Data Risk Analysis"
  company: one
  registry_activity: one
  creation_date: "<%= Date.today.to_s %>"
  property: 0
  danger: "Unauthorized access to customer personal data"
  vulnerability_description: "Weak password policies and lack of encryption"
  security: "Implement strong password policies, enable two-factor authentication, and encrypt sensitive data"
  probability: 3
  effect: 4
  risk: 2
  data_processing_impact_assessment: 0

two:
  name: "HR Data Risk Analysis"
  company: one
  registry_activity: two
  creation_date: "<%= Date.today.to_s %>"
  property: 2
  danger: "Data corruption or unauthorized modification of employee records"
  vulnerability_description: "Insufficient access controls and lack of data backup procedures"
  security: "Implement role-based access controls and regular data backups"
  probability: 2
  effect: 3
  risk: 1
  data_processing_impact_assessment: 1

three:
  name: "System Availability Risk Analysis"
  company: two
  registry_activity: one
  creation_date: "<%= Date.today.to_s %>"
  property: 1
  danger: "System downtime affecting business operations"
  vulnerability_description: "Single point of failure in critical systems"
  security: "Implement redundancy and failover mechanisms"
  probability: 1
  effect: 5
  risk: 1
  data_processing_impact_assessment: 0

four:
  name: "Critical Security Risk Analysis"
  company: one
  registry_activity: one
  creation_date: "<%= Date.today.to_s %>"
  property: 0
  danger: "Complete data breach exposing all customer information"
  vulnerability_description: "Outdated security systems and unpatched vulnerabilities"
  security: "Immediate security system upgrade and comprehensive vulnerability patching"
  probability: 5
  effect: 5
  risk: 3
  data_processing_impact_assessment: 0

five:
  name: "Low Risk Analysis"
  company: two
  registry_activity: two
  creation_date: "<%= Date.today.to_s %>"
  property: 1
  danger: "Minor system performance degradation"
  vulnerability_description: "Occasional network latency issues"
  security: "Monitor network performance and optimize as needed"
  probability: 1
  effect: 1
  risk: 0
  data_processing_impact_assessment: 1
