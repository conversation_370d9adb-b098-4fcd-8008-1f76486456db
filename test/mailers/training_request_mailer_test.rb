require 'test_helper'

class TrainingRequestMailerTest < ActionMailer::TestCase
  let(:training_request) { training_requests(:wiktoria_training_request) }

  test 'training_request_created' do
    email = TrainingRequestMailer.training_request_created(training_request.id)

    assert_match 'New training request', email.subject
    assert_includes email.to, training_request.user.department.chief.email
    assert_includes email.cc,
                    User.joins(:global_roles).find_by(global_roles: { remote_work_periods_notifications: true }).email
  end

  test 'training_request_accepted' do
    email = TrainingRequestMailer.training_request_accepted(training_request.id)

    assert_match 'Training request has been accepted', email.subject
    assert_equal [training_request.user.email], email.to
    assert_includes email.cc,
                    User.joins(:global_roles).find_by(global_roles: { remote_work_periods_notifications: true }).email
  end

  test 'training_request_rejected' do
    email = TrainingRequestMailer.training_request_rejected(training_request.id)

    assert_match 'Training request has been rejected', email.subject
    assert_equal [training_request.user.email], email.to
    assert_includes email.cc,
                    User.joins(:global_roles).find_by(global_roles: { remote_work_periods_notifications: true }).email
  end

  test 'remote_work_period_reminder' do
    email = TrainingRequestMailer.training_request_reminder(training_request)

    assert_match "Training request for #{training_request.user.full_name} is waiting for acceptance", email.subject
    assert_includes email.to, training_request.user.department.chief.email
  end
end
