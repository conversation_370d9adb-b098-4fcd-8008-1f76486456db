class SurveyAnswerPolicy < ApplicationPolicy
  class Scope < Scope
    def resolve
      scope.pending.where(user: user)
    end
  end

  def index?
    internal_user? && !separate_company?
  end

  def show?
    (record.pending? && record.user == user) ||
      record.evaluation.user.department.chief == user ||
      user.board_member?
  end

  def update?
    internal_user? && !separate_company?
  end
end
