class CostProject < ApplicationRecord
  belongs_to :cost_invoice, inverse_of: :cost_projects
  belongs_to :accounting_number, -> { with_deleted }, inverse_of: :cost_projects
  belongs_to :department, optional: true
  belongs_to :cost_account_number, optional: true

  validates :cost_invoice, presence: true
  validates :department, presence: true, if: -> { cost_invoice.is_a?(Dms::CostInvoice) }
  validates :amount, presence: true, numericality: true
  validates :cost_account_number, presence: true, if: -> { cost_invoice.is_a?(Dms::CostInvoice) && cost_invoice.accepted? }
  validate :accounting_number_active

  def cost_accounting_number
    cost_account_number&.number
  end

  private

  def accounting_number_active
    return unless accounting_number_id_changed?
    return unless accounting_number&.deleted? || accounting_number&.locked?
    return if cost_invoice.force_accept

    errors.add(:accounting_number_id, :not_allowed)
  end
end
