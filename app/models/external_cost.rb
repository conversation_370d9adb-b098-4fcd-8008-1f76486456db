class ExternalCost < ApplicationRecord
  LAST_SAVE_DAY = 2

  belongs_to :project
  belongs_to :contractor
  belongs_to :mpk_number
  belongs_to :created_by, class_name: 'User', optional: true

  validates :amount, numericality: { greater_than: 0 }
  validates :cost_date, :currency, :comment, presence: true

  enum currency: CURRENCIES

  def min_cost_date
    Date.current.day <= LAST_SAVE_DAY ? Date.current.prev_month.beginning_of_month : Date.current.beginning_of_month
  end

  def due?
    return unless cost_date

    [cost_date_was, cost_date].compact.min < min_cost_date
  end
end
