class Membership < ApplicationRecord
  include Documentation::MembershipModel
  include PublicActivity::Model

  MEMBER_TYPES = %w[User Group].freeze

  belongs_to :member, polymorphic: true, touch: true
  belongs_to :project
  has_many :membership_roles, dependent: :destroy
  # Notice that after_destroy callbacks will not be triggered on the associated entity when using has_many :through
  # If the :through option is true callbacks in the join models are triggered except destroy callbacks,
  # since deletion is direct.
  has_many :roles, through: :membership_roles

  has_many :direct_membership_roles, -> { where(inherited_from: nil) },
           dependent: :destroy, class_name: 'MembershipRole', inverse_of: :membership
  has_many :direct_roles, through: :direct_membership_roles, source: :role,
                          before_remove: :mark_parent_membership_roles_for_destruction,
                          after_remove: %i[remove_inherited_membership_roles touch_self touch_role
                                           touch_project save_destroy_membership_role_activity],
                          after_add: %i[touch_self touch_role touch_project]

  validates :member, :project, presence: true
  validates :project, uniqueness: { scope: %i[member_id member_type] }
  validates :member_type, inclusion: { in: MEMBER_TYPES }
  validates :direct_roles, presence: true, if: lambda {
    direct_membership_roles.empty? && membership_roles.empty?
  }

  attr_reader :destruction_initiated

  scope :of_user_and_groups, lambda { |user|
    where("(member_id = ? AND member_type = 'User') \
          OR \
          (member_id IN (?) AND member_type = 'Group')".squish,
          user.id, user.group_ids)
  }

  after_create :save_create_activity, if: -> { member_type == 'User' }
  before_destroy :touch_project, unless: -> { @dont_touch_project }
  after_destroy :save_destroy_activity, if: -> { member_type == 'User' }
  after_save :update_members_in_project, if: -> { member_type == 'Group' }
  after_save :update_approvals
  after_save :destroy, if: -> { reload.roles.empty? }
  after_create_commit :touch_project
  after_commit :destroy_redmine, on: :destroy
  after_commit :update_redmine, on: %i[create update]

  def destroy
    @destruction_initiated = true
    super
  end

  def destroy_without_project_touch
    @dont_touch_project = true
    destroy
  end

  def destroyable?
    membership_roles.where.not(inherited_from: nil).none?
  end

  private

  def save_create_activity
    project.create_activity key: 'project.add_member',
                            parameters: { user: member.username, roles: roles.pluck(:name) }
  end

  def save_destroy_activity
    project.create_activity key: 'project.remove_member', parameters: { user: member.username }
  end

  def activity_custom_params
    { member: member.is_a?(User) ? member.username : member.name }
  end

  def update_approvals
    project.project_agreements.published.find_each do |project_agreement|
      ApprovalsForProjectAgreementWorker.perform_async(project_agreement.id)
    end
  end

  def mark_parent_membership_roles_for_destruction(role)
    parent_membership_roles_to_remove << direct_membership_roles.find_by(role: role)
  end

  def remove_inherited_membership_roles(_role)
    MembershipRole.where(inherited_from_id: parent_membership_roles_to_remove).destroy_all
  end

  def save_destroy_membership_role_activity(role)
    return unless member_type == 'User'

    project.create_activity key: 'project.remove_role_from_member',
                            parameters: { user: member.username, role: role.name }
  end

  def parent_membership_roles_to_remove
    @parent_membership_roles_to_remove ||= []
  end

  def update_members_in_project
    member.users.each do |user|
      membership = project.memberships.detect { |m| m.member_id == user.id && m.member_type == 'User' } ||
                   project.memberships.build(member: user)
      direct_membership_roles.each do |membership_role|
        membership.membership_roles.find_or_initialize_by(role_id: membership_role.role_id, inherited_from: membership_role)
      end
      membership.save
    end
  end

  def touch_project(*_)
    project.try(:touch) if project&.persisted?
  end

  # role # removed role (delete_all/destroy membership_roles)
  def touch_role(role)
    role.touch if role.persisted?
  end

  # self # membership
  def touch_self(_)
    touch if persisted?
  end

  def update_redmine
    return unless proceed_redmine?

    RedmineMembershipsWorker.perform_in(10.seconds, 'update_member', id) # give some time to create project
  end

  def destroy_redmine
    return unless proceed_redmine?

    RedmineMembershipsWorker.perform_async('remove_member', project_id, member_id, member_type)
  end

  def proceed_redmine? # rubocop:disable Metrics/AbcSize,Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
    return false unless project&.active?
    return false if member.is_a?(User) && !member.redmine && !member.redmine_id
    return false if project&.company&.click_up_synchronization? && !project.cooperative_project?

    true
  end
end
