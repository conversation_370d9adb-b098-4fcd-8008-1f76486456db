module Documentation
  module PaymentScheduleModel
    include Swagger::Blocks
    extend ActiveSupport::Concern

    included do
      swagger_schema :PaymentScheduleResponse do
        key :required, [:id, :created_at, :updated_at, :url, :cache_ts]
        key :id, :PaymentSchedule
        property :id do
          key :type, :integer
          key :format, :int32
          key :maximum, 2_147_483_647
        end
        property :created_at do
          key :type, :string
          key :maximum, 255
        end
        property :updated_at do
          key :type, :string
          key :maximum, 255
        end
        property :url do
          key :type, :string
          key :maximum, 255
        end
        property :cache_ts do
          key :type, :string
          key :maximum, 255
        end
      end
      swagger_schema :PaymentSchedule do
        property :payments_attributes do
          key :required, [:issued_on, :predicted_amount, :description]
          key :type, :array
          key :description, 'payment attributes'
          items do
            property :id do
              key :type, :integer
            end
            property :issued_on do
              key :type, :date
            end
            property :predicted_amount do
              key :type, :integer
            end
            property :description do
              key :type, :string
            end
          end
        end
      end
      swagger_schema :PaymentScheduleRequest do
        key :required, [:payment_schedule]
        property :payment_schedule do
          key :'$ref', :PaymentSchedule
        end
      end
    end
  end
end
