module ProjectLdapEntryable
  extend ActiveSupport::Concern
  include LdapEntryable

  included do
    before_validation :create_gid_number,
                      if: -> { gid_number.blank? && !new_record? }
    before_create :create_gid_number

    before_destroy :archive!
  end

  def dn
    "cn=#{cn}"
  end

  def cn
    identifier
  end

  def ldap_raw_attributes(tree)
    data = {
      objectClass: %w(top posixGroup extensibleObject),
      cn: cn,
      description: name,
      gidNumber: gid_number.to_s
    }

    ldap_users(tree[:membership_column], tree[:include_board]).each_with_index do |user, index|
      data[:memberUid] = [] if index.zero?
      data[:memberUid] << user.username
    end

    (data[:info] ||= []) << (active? ? 'active' : 'inactive')
    data[:info] << 'serviceCloudshare' if owncloud?
    data[:info] << 'serviceDocscloud' if docs_cloud?
    data[:info] << 'serviceChat' if chat?
    data[:info] << 'serviceCi' if jenkins?

    data
  end

  def should_exist_ldap?
    active? || owncloud? || docs_cloud?
  end

  private

  def ldap_users(membership_column, include_board)
    membership_column ||= :ldap
    memberships_with_roles = memberships.joins(:roles)
    from_group = memberships_with_roles.where(member_type: 'Group',
                                              roles: { membership_column => true })
                                        .includes(member: :users).map(&:member).map(&:users)
                                        .flatten
    direct = memberships_with_roles.where(member_type: 'User',
                                          roles: { membership_column => true })
                                    .includes(:member).map(&:member)
    (from_group + direct + from_board(include_board)).uniq.select(&:active?)
  end

  def from_board(include_board)
    include_board ? User.board_members.active : []
  end

  def create_gid_number
    last_gid_number = [
      settings[:min_project_gid_number].to_i,
      Project.order('gid_number DESC').first.try(:gid_number) || 0
    ].max
    self.gid_number = last_gid_number + 1
  end
end
