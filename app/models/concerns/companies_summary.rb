module CompaniesSummary
  SELECT_CLAUSE = 'companies.id, companies.name,
    COALESCE(SUM(payments.predicted_amount), 0) AS payments_sum,
    COALESCE(SUM(external_costs.amount), 0) AS costs,
    COALESCE(SUM(payments.predicted_amount), 0) -
      COALESCE(SUM(external_costs.amount), 0) AS scheduled_sum,
    COALESCE(SUM(invoices.total_amount), 0) AS paid_sum,
    payments.currency'.freeze

  def for_accounting(opts = {})
    scope = select(SELECT_CLAUSE).joins(join_clause(opts || {}))
                                 .where(accounting: true)
                                 .group('companies.id, payments.currency')
                                 .order(:id)

    if opts[:mpk_number_ids].present?
      scope = scope.where(
        payment_mpk_positions: { mpk_number_id: opts[:mpk_number_ids] }
      )
    end

    scope
  end

  private

  def join_clause(opts) # rubocop:disable Metrics/MethodLength
    projects_clause(opts) + 'INNER JOIN payment_schedules
    ON payment_schedules.project_id = projects.id ' + payments_clause(opts) + mpks_clause(opts) +
      'LEFT JOIN (SELECT SUM(amount) AS amount, project_id FROM external_costs
        WHERE external_costs.cost_date BETWEEN' + "'#{opts[:date_from]}' AND '#{opts[:date_to]}'" + '
     GROUP BY project_id) AS external_costs
     ON external_costs.project_id = projects.id
     LEFT JOIN (
       SELECT a.total_amount, a.payment_id
         FROM invoices a
         INNER JOIN (
           SELECT payment_id, MAX(created_at) created_at
           FROM invoices
           WHERE state = 1
           GROUP BY payment_id
         ) b ON a.payment_id = b.payment_id AND a.created_at = b.created_at
       ) invoices ON invoices.payment_id = payments.id'
  end

  def payments_clause(opts)
    base = 'INNER JOIN payments
            ON payments.payment_schedule_id = payment_schedules.id AND payments.deleted_at IS NULL '

    base + "AND payments.issued_on BETWEEN '#{opts[:date_from]}' AND '#{opts[:date_to]}' "
  end

  def mpks_clause(opts)
    mpk_number_ids = opts.dig(:mpk_number_ids)

    return '' if mpk_number_ids.blank?

    <<~SQL
      INNER JOIN payment_mpk_positions ON payment_mpk_positions.payment_id = payments.id
    SQL
  end

  def handle_multiple(params)
    case params
    when Array
      params.map { |param| sanitize_sql(param) }.join(',')
    else
      sanitize_sql(params)
    end
  end

  def projects_clause(opts) # rubocop:disable Metrics/MethodLength
    base = <<~SQL
      INNER JOIN projects ON projects.company_id = companies.id
    SQL

    if opts[:of_client].present?
      base += <<~SQL
        AND projects.client_id IN (#{handle_multiple(opts[:of_client])})
      SQL
    end

    if opts[:accounting_number_ids].present?
      base += <<~SQL
        AND projects.accounting_number_id IN (#{handle_multiple(opts[:accounting_number_ids])})
      SQL
    end

    if opts[:of_responsible].present?
      base = <<~SQL
        LEFT JOIN (
          SELECT p.id, p.company_id, p.currency
          FROM projects p
          INNER JOIN memberships ON memberships.project_id = p.id
            AND memberships.member_type = 'User'
            AND memberships.member_id IN (#{handle_multiple(opts[:of_responsible])})
          INNER JOIN membership_roles
            ON memberships.id = membership_roles.membership_id
          INNER JOIN roles
            ON roles.id = membership_roles.role_id AND roles.responsible = 1
          GROUP BY p.id
        ) projects on projects.company_id = companies.id
      SQL
    end
    base
  end
end
