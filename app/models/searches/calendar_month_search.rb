module Searches
  class CalendarMonthSearch
    include SearchObject.module(:model)
    include Concerns::DepartmentSearchable

    option :user_id do |scope, value|
      scope.where(user_id: value) if value.present?
    end

    option :user_redmine_id do |scope, value|
      scope.joins(:user).where(users: { redmine: true, redmine_id: value }) if value.present?
    end

    option :departments_ids do |scope, value|
      scope.joins(:user).where(users: { department_id: searchable_department_ids(value) }) if value.present?
    end

    option :project_id do |scope, value|
      scope.joins(user: :memberships).where(memberships: { project_id: value }) if value.present?
    end

    option :year do |scope, value|
      scope.where(year: value) if value.present?
    end

    option :month do |scope, value|
      scope.where(month: value) if value.present?
    end

    option :years_months do |scope, value|
      value.map do |year, month|
        scope.where(year: year, month: month)
      end.reduce(:or)
    end
  end
end
