module Pages
  class Profile
    include Documentation::Pages::ProfileModel
    include ::Draper::Decoratable

    attr_accessor :user

    delegate :first_password, :absence_quota, :absence_balance,
             to: :user,
             allow_nil: true

    def initialize(user)
      @user = user
    end

    def cache_key
      "profiles/#{to_param}-#{updated_at.utc.to_fs(:nsec)}"
    end

    def to_param
      @as_param ||= Digest::SHA1.hexdigest({
        user: user.serializable_hash,
        memberships: user.memberships.ids,
        global_roles: user.global_roles.ids
      }.flatten.join('-'))
    end

    def updated_at
      @max_updated_at ||= [
        user.updated_at,
        user.memberships.maximum(:updated_at),
        user.memberships.joins(:roles).maximum('roles.updated_at'),
        user.global_roles.maximum(:updated_at)
      ].compact.max
    end

    def global_admin?
      user.present? && UserPolicy.new(user, User).send(:global_admin?)
    end

    def privileged_user?
      user.present? && UserPolicy.new(user, User).send(:privileged_user?)
    end

    def hr_user?
      user.present? && UserPolicy.new(user, User).send(:hr_user?)
    end
  end
end
