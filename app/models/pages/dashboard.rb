require 'date_utils'

module Pages
  class Dashboard
    include ::Draper::Decoratable

    attr_reader :user

    def initialize(user)
      @user = user
    end

    def cache_key # rubocop:disable Metrics
      @cache_key ||= begin
        hexdigest = Digest::SHA1.hexdigest([
          user.cache_key_with_version,
          show_b2b_reminder,
          show_payments_reminder && payments_to_remind.cache_key_with_version,
          show_new_hr_cost_invoice_shortcut,
          show_new_dms_cost_invoice_shortcut,
          show_new_holiday_request_shortcut,
          show_new_admin_ticket_shortcut,
          show_new_asset_shortcut,
          show_available_holidays && available_holidays.values,
          show_my_holiday_requests && my_holiday_requests.cache_key_with_version,
          show_my_dms_cost_invoices && my_dms_cost_invoices.cache_key_with_version,
          show_teams_assets_requests && teams_assets_requests.cache_key_with_version,
          show_employees_dms_cost_invoices && employees_dms_cost_invoices.cache_key_with_version,
          show_employees_hr_cost_invoices && employees_hr_cost_invoices.cache_key_with_version,
          show_employees_next_weeks_holidays && employees_next_weeks_holidays.cache_key_with_version,
          show_employees_holiday_requests && employees_holiday_requests.cache_key_with_version,
          show_employees_assets_requests && employees_assets_requests.cache_key_with_version
        ].join('-'))

        "dashboard/#{hexdigest}"
      end
    end

    def show_b2b_reminder
      @show_b2b_reminder ||= last_work_day_of_month? && b2b?
    end

    def show_payments_reminder
      @show_payments_reminder ||= payments_to_remind.any?
    end

    def payments_to_remind
      @payments_to_remind ||= Payment.without_issued_invoice
                                     .includes(payment_schedule: { project: { memberships: :roles } })
                                     .where(payments: { first_remind_at: ...Time.current })
                                     .where(projects: { status: 0 })
                                     .where(memberships: { member: user })
                                     .where(roles: { responsible: true })
    end

    def show_new_hr_cost_invoice_shortcut
      @show_new_hr_cost_invoice_shortcut ||= hr_cost_invoice_policy.new?
    end

    def show_new_dms_cost_invoice_shortcut
      @show_new_dms_cost_invoice_shortcut ||= dms_cost_invoice_policy.new?
    end

    def show_new_holiday_request_shortcut
      @show_new_holiday_request_shortcut ||= user.native?
    end

    def show_new_admin_ticket_shortcut
      @show_new_admin_ticket_shortcut ||= user.native? && request_tracker_issue_policy.create?
    end

    def show_new_asset_shortcut
      @show_new_asset_shortcut ||= user.native? && asset_policy.create?
    end

    def show_available_holidays
      @show_available_holidays ||= user.native?
    end

    def available_holidays
      @available_holidays ||= begin
        decorated_user = user.decorate

        {
          absence_quota: decorated_user.absence_quota,
          absence_count_this_year: decorated_user.absence_count_this_year,
          absence_balance: decorated_user.absence_balance
        }
      end
    end

    def show_my_holiday_requests
      @show_my_holiday_requests ||= user.native? && holiday_request_policy.index? && !show_employees_holiday_requests
    end

    def my_holiday_requests
      @my_holiday_requests ||= user.applicant_holiday_requests
                                   .where(ends_on: Time.zone.today..)
    end

    def show_my_dms_cost_invoices
      @show_my_dms_cost_invoices ||= dms_cost_invoice_policy.index? && !show_employees_dms_cost_invoices
    end

    def my_dms_cost_invoices
      @my_dms_cost_invoices ||= Dms::CostInvoice.where(user_id: user.id)
                                                .includes(:contractor, :cost_invoice_positions)
    end

    def show_teams_assets_requests
      @show_teams_assets_requests ||= user.native? && project_manager? && !show_employees_assets_requests
    end

    def teams_assets_requests
      @teams_assets_requests ||= AssetPolicy::Scope.new(user, Asset.pending)
                                                   .resolve
                                                   .includes(:requester)
    end

    def show_employees_dms_cost_invoices
      @show_employees_dms_cost_invoices ||= dms_cost_invoice_policy.index? && (department_chief_or_substitute_chief? ||
                                                                                department_uber_chief? ||
                                                                                department_supervisor?)
    end

    def employees_dms_cost_invoices
      @employees_dms_cost_invoices ||= begin
        scope = Dms::CostInvoicePolicy::Scope.new(user, Dms::CostInvoice.pending).resolve
        filters = { to_my_accept: true }

        Searches::Dms::CostInvoiceSearch.results({ scope: scope, filters: filters }, user)
                                        .includes(:contractor, :cost_invoice_positions)
      end
    end

    def show_employees_hr_cost_invoices
      @show_employees_hr_cost_invoices ||= hr_cost_invoice_policy.index? && (department_chief_or_substitute_chief? ||
                                                                              department_uber_chief? ||
                                                                              department_supervisor?)
    end

    def employees_hr_cost_invoices
      @employees_hr_cost_invoices ||= begin
        scope = B2B::CostInvoicePolicy::Scope.new(user, B2B::CostInvoice.pending_department).resolve
        scope.includes(:contractor, :cost_invoice_positions)
             .where.not(user_id: user.id)
      end
    end

    def show_employees_next_weeks_holidays
      @show_employees_next_weeks_holidays ||= user.native? &&
                                              (department_chief_or_substitute_chief? ||
                                                department_uber_chief? ||
                                                department_supervisor?)
    end

    def employees_next_weeks_holidays # rubocop:disable Metrics/AbcSize
      @employees_next_weeks_holidays ||= begin
        scope = HolidayRequestPolicy::Scope.new(user, HolidayRequest.visible).resolve.where.not(applicant_id: user.id)
        scope = scope.joins(applicant: :department)
                     .left_joins(applicant: :departments_as_chief)
                     .left_joins(applicant: :departments_as_uber_chief)
                     .includes(:applicant)
                     .between(Time.zone.today.next_week, Time.zone.today.next_week.end_of_week)

        department_ids = Department.in_chief_or_substitute_chief(user).pluck(:id)

        scope.where(departments: { id: department_ids }).distinct
      end
    end

    def show_employees_holiday_requests
      @show_employees_holiday_requests ||= user.native? && (department_chief_or_substitute_chief? ||
                                                            department_uber_chief? ||
                                                            department_supervisor?)
    end

    def employees_holiday_requests # rubocop:disable Metrics/MethodLength
      @employees_holiday_requests ||= begin
        scope = HolidayRequestPolicy::Scope.new(user, HolidayRequest.pending).resolve
        scope.joins(applicant: :department)
             .includes(:applicant)
             .where.not(applicant_id: user.id)
             .where(
               <<~SQL.squish,
                 departments.uber_chief_id = :user_id OR
                 (departments.chief_id = :user_id AND
                   (departments.uber_chief_id IS NULL OR departments.uber_chief_id != holiday_requests.applicant_id)) OR
                 (departments.substitute_chief_id = :user_id AND
                   (departments.uber_chief_id IS NULL OR departments.uber_chief_id != holiday_requests.applicant_id) AND
                   (departments.chief_id IS NULL OR departments.chief_id != holiday_requests.applicant_id)) OR
                 (departments.supervisor_id = :user_id AND
                   (departments.uber_chief_id IS NULL OR departments.uber_chief_id != holiday_requests.applicant_id) AND
                   (departments.chief_id IS NULL OR departments.chief_id != holiday_requests.applicant_id) AND
                   (departments.substitute_chief_id IS NULL OR departments.substitute_chief_id != holiday_requests.applicant_id))
               SQL
               user_id: user.id
             )
      end
    end

    def show_employees_assets_requests
      @show_employees_assets_requests ||= user.native? && (department_chief? || department_uber_chief? || department_supervisor?)
    end

    def employees_assets_requests
      @employees_assets_requests ||= AssetPolicy::Scope.new(user, Asset.pending)
                                                       .resolve
                                                       .includes(:requester)
    end

    private

    def last_work_day_of_month?
      today = Date.current
      DateUtils.last_work_day_of_month?(today) || (today..today.end_of_month).select { user.working_day?(_1) } == [today]
    end

    def b2b?
      user.current_contract&.b2b? && user.contractor.present?
    end

    def project_manager?
      @project_manager ||= user.roles.where(roles: { pm: true }).any?
    end

    def department_chief?
      @department_chief ||= user.departments_as_chief.any?
    end

    def department_uber_chief?
      @department_uber_chief ||= user.departments_as_uber_chief.any?
    end

    def department_supervisor?
      @department_uber_chief ||= user.departments_as_supervisor.any?
    end

    def department_chief_or_substitute_chief?
      @department_chief_or_substitute_chief ||= Department.in_chief_or_substitute_chief(user.id).any?
    end

    def hr_cost_invoice_policy
      @hr_cost_invoice_policy ||= B2B::CostInvoicePolicy.new(user, B2B::CostInvoice)
    end

    def dms_cost_invoice_policy
      @dms_cost_invoice_policy ||= Dms::CostInvoicePolicy.new(user, Dms::CostInvoice)
    end

    def holiday_request_policy
      @holiday_request_policy ||= HolidayRequestPolicy.new(user, HolidayRequest)
    end

    def request_tracker_issue_policy
      @request_tracker_issue_policy ||= RequestTrackerIssuePolicy.new(user, RequestTrackerIssue)
    end

    def asset_policy
      @asset_policy ||= AssetPolicy.new(user, Asset)
    end
  end
end
