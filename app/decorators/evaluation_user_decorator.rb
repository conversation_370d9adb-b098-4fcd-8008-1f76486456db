class EvaluationUserDecorator < ApplicationDecorator
  delegate_all

  def last_evaluation_on
    evaluation = evaluations.to_a.sort_by(&:starts_on).reverse.detect do |e|
      e.starts_on < Time.zone.today
    end
    evaluation.try(:starts_on)
  end

  def next_evaluation_on
    evaluation = evaluations.to_a.sort_by(&:starts_on).detect do |e|
      e.starts_on > Time.zone.today
    end
    evaluation.try(:starts_on)
  end
end
