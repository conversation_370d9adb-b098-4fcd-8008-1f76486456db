class AssetMailer < ApplicationMailer
  def to_accept(asset)
    @asset = asset
    @project = asset.project
    mail(to: receivers, subject: 'New Asset requires acceptation') if receivers.present?
  end

  def pending_assets(recipient, assets)
    @assets = assets

    mail(to: recipient, subject: 'Assets are pending acceptance')
  end

  def expires_soon(project_id, assets)
    @assets = assets
    recipients = get_pms_and_accounters(project_id).pluck(:email)

    mail(to: recipients, subject: 'Asset expires soon') if recipients.present?
  end

  def decommissioned(asset)
    @asset = asset
    @project = asset.project
    @user = asset.user if asset.type == 'Vpn'

    mail(to: asset.passed_to_decommission_by.email, subject: 'Asset has been decommissioned')
  end

  private

  def receivers
    @receivers ||= begin
      return project_managers if @project

      chiefs
    end
  end

  def project_managers
    User.active.project_pms(@project.id).pluck(:email)
  end

  def chiefs
    @asset.user&.department&.chief&.email
  end

  def get_pms_and_accounters(project_id)
    User.active.project_pms_and_accounters(project_id)
  end
end
