module Evaluations
  class EvaluationMailer < ApplicationMailer
    def additional_users(target_users, subject_user)
      @subject_user = subject_user
      subject = 'Nowa ankieta do wypełnienia'
      mail(to: target_users.map(&:email), subject: subject)
    end

    def created(subject_user)
      subject = 'Ankieta ewaluacyjna do wypełnienia'
      mail(to: subject_user.email, subject: subject)
    end
  end
end
