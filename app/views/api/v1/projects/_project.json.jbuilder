project = project.decorate unless project.decorated?
json.partial! '/api/v1/escape_json', locals: {
  model_attributes: ProjectDecorator.attribute_names_visible_to_all,
  resource: project
}
json.extract! project, :owncloud, :docs_cloud, :sla, :sla_start_date, :sla_end_date
json.company_name h(project.company_name)
json.parent_name h(project.parent_name)
subprojects = project.subprojects
json.subprojects subprojects
json.subprojects_count subprojects.size
json.url project_url(project, format: :json)
json.cache_ts((Time.current.to_f * 1000).ceil)
json.invoices_visible project.payment_schedule && project.payment_schedule.invoices.any?
json.actions_for_current_user project.actions_for_current_user(current_user)
