json.registry_activity do
  json.extract!(@registry_activity, :id, :description, :activity, :administrator_type,
                :administrator_type_custom, :co_administrator_type, :co_administrator_type_custom,
                :data_processing_goals, :legal_basis, :subjects_categories, :personal_data_categories,
                :categories_of_recipients, :transfer_to_third_country, :planned_deletion_dates,
                :security_measures, :creation_date, :expiration_date)
end

json.administrator_types RegistryActivity.administrator_types.keys
json.co_administrator_types RegistryActivity.co_administrator_types.keys
