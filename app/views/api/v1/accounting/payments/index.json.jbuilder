json.array! @payments do |payment|
  project = payment.payment_schedule.project

  json.extract! payment, :id, :issued_on, :sell_date, :description, :predicted_amount,
                :cyclic, :cycle_length, :originator_id, :ends_on, :currency
  json.invoice_no payment.current_invoice.try(:id)
  json.pending_invoice_no payment.pending_invoice.try(:id)
  json.accepted_invoice_no payment.accepted_invoice.try(:id)

  json.project do
    json.extract! project, :id, :name, :identifier
  end

  json.mpk_positions_attributes do
    json.array!(payment.mpk_positions) do |mpk_position|
      json.extract! mpk_position, :id, :amount, :mpk_number_id, :project_id
      json.mpk_number_key mpk_position.mpk_number.key
      json.mpk_number_full_name mpk_position.mpk_number.decorate.full_name
    end
  end
end
