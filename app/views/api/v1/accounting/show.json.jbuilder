json.subproject_count @project.descendants.count
json.extract! @project, :client_name, :currency, :account_number
json.account_managers responsible_for(@project)
json.accounting_number do
  if @project.accounting_number
    json.partial!('/api/v1/accounting_numbers/accounting_number', accounting_number: @project.accounting_number)
  else
    json.nil!
  end
end

json.payments_summary payments_summary(@projects_by_currency)

json.mpks_summary do
  json.array!(@payments_mpk_summary) do |month, mpks|
    json.month month
    json.mpks do
      json.array!(mpks) do |mpk|
        json.extract! mpk, :id, :mpk_name, :mpk_key, :currency, :paid, :scheduled_payments, :balance
      end
    end
  end
end

json.invoices_summary do
  json.array!(@payments_invoices_summary) do |month, invoices|
    json.month month
    json.invoices do
      json.array!(invoices) do |invoice|
        json.extract! invoice, :id, :title, :currency, :paid, :scheduled_payments, :balance
      end
    end
  end
end

json.payments_per_month do
  json.array!(@payments_per_month) do |month, payments|
    json.month month
    json.scheduled_payments do
      json.array!(payments) do |payment|
        json.extract! payment, :amount, :currency
      end
    end
  end
end
