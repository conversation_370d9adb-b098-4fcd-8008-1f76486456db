json.invoice do
  if @invoice
    json.amendment @invoice.amendment?
    json.draft @invoice.draft?
    json.partial! 'invoice', locals: { invoice: @invoice }
  else
    json.amendment false
    json.draft false
    json.partial! 'default_invoice_data'
  end
  json.currency @payment.currency
end

json.secondary_data do
  json.partial! 'secondary_data'
end

if @payment.originator_id
  invoice = Invoice.of_cycle(@payment).issued.order(invoice_date: :desc).first
  if invoice
    json.last_cycle_invoice do
      json.mpk_positions_attributes do
        json.array!(invoice.mpk_positions.includes(:mpk_number)) do |mpk_position|
          json.extract! mpk_position, :amount, :mpk_number_id, :project_id
          json.mpk_number_full_name mpk_position.mpk_number.decorate.full_name
        end
      end

      json.invoice_positions_attributes do
        json.array!(invoice.invoice_positions) do |invoice_position|
          json.extract! invoice_position, :name, :amount, :tax_rate, :unit_price, :net_value
        end
      end
    end
  end
end

json.revenue_accounts do
  json.array! @revenue_accounts do |revenue_account|
    json.extract!(revenue_account, :id, :key, :name)
  end
end

json.attachments do
  json.array! @invoice&.attachments || [] do |attachment|
    json.extract! attachment, :id
    json.file JSON.parse(attachment.file_data)
    json.file_url file_attachments_url(attachment)
    json.required @invoice.attachment_required?(attachment)
  end
end

json.required_attachments do
  json.array! @invoice&.required_attachments || [] do |attachment|
    json.extract! attachment, :id, :attachment_id
  end
end

json.mpk_numbers do
  json.array! @mpk_numbers do |mpk_number|
    json.extract!(mpk_number.decorate, :id, :full_name)
  end
end

json.tax_rates do
  json.array! @tax_rates do |tax_rate|
    json.key tax_rate
    json.name InvoicePosition.human_attribute_name("tax_rate.#{tax_rate}")
  end
end

json.kinds do
  json.array! @kinds do |kind|
    json.key kind
    json.name Invoice.human_attribute_name("kind.#{kind}")
  end
end

json.advance_invoices do
  json.array! @advance_invoices do |advance_invoice|
    json.extract! advance_invoice, :id, :number
  end
end
