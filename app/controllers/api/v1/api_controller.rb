# https://github.com/plataformatec/responders/blob/master/lib/action_controller/responder.rb
module Api
  module V1
    class ApiController < ApplicationController
      include ::Api::V1::Concerns::Api
      include ::Api::V1::Concerns::Documentation::RandomEndpoint

      include PublicActivity::StoreController
      # include ActionView::Helpers::TextHelper

      self.responder = ApplicationResponder
      respond_to :json
      layout false

      prepend_before_action :authenticate_user!, unless: lambda {
        api_key_request? || swagger_request? || doorkeeper_request?
      }
      prepend_before_action :authenticate_api_key!, if: :api_key_request?
      prepend_before_action :authenticate_swagger_user!, if: :swagger_request?
      prepend_before_action :doorkeeper_authorize!, if: :doorkeeper_request?

      after_action :set_content_type
      before_action :log_request_info
      before_action :set_userstamping_gid

      # Handle AASM Transition exceptions
      rescue_from AASM::InvalidTransition, with: :render_aasm_error

      def route_not_found
        skip_authorization
        skip_policy_scope
        render json: { errors: { base: 'Invalid route' } }, status: :not_found
      end

      protected

      def search_options(options = {})
        ConfigureDefaultPaginationInteractor.call(params: options).params
      end
      alias adjust_pagination search_options

      def paginate_list(results, params)
        paginate(results, adjust_pagination(params))
      end

      private

      def filter
        params.fetch(:filter) { {} }
      end

      def includes
        params.fetch(:include, '').split(',').map(&:strip)
      end

      def page_number
        page.fetch(:number) { 1 }
      end

      def per_page
        page.fetch(:size) { Kaminari.config.default_per_page }
      end

      def page
        params.fetch(:page) { {} }
      end
      ##########################################################################

      def set_userstamping_gid
        RequestStore.store[:userstamping_gid] = current_user.try(:to_global_id).to_s
        RequestStore.store[:x_request_id] = request.uuid
      end

      def set_content_type
        self.content_type = "application/vnd.api+json; version=#{api_version.to_s[/\d+/]}"
      end

      def api_key_request?
        request.headers['X-Api-Key'].present?
      end

      def doorkeeper_request?
        doorkeeper_token.present?
      end

      def doorkeeper_authorize!
        super

        @current_user = User.find(doorkeeper_token.resource_owner_id)
      end

      def authenticate_api_key!
        @current_user = ApiKey.valid.validate_by_key(request.headers['X-Api-Key'])
      end

      def swagger_request?
        Settings.allow_impersonification_without_authentication && request.headers['X-Swagger-Sign-In-As']
      end

      def authenticate_swagger_user!
        user = User.where(id: request.headers['X-Swagger-Sign-In-As']).first!
        request.env['devise.skip_trackable'] = true
        @current_user = sign_in(:user, user, store: false, bypass: false) && user
      end

      def log_request_info
        return unless user_signed_in?

        Rails.logger.info "USER ID ##{current_user.id}"
        Rails.logger.info "IMPERSONATOR ID ##{current_user.impersonator_id}"
      end

      def handle_password_change
        if request.format.html?
          super
        else
          return if warden.nil?

          if !devise_controller? and !ignore_password_expire? # and not request.format.nil?
            Devise.mappings.keys.flatten.any? do |scope|
              # re-check to avoid infinite loop if date changed after login attempt
              # if send(:"current_#{scope}").try(:need_change_password?)
              # respond_to do |format|
              #  format.json { render json: { errors: { base: "Password expired" } }, status: 409 }
              # end
              if signed_in?(scope) and send(:"current_#{scope}").try(:need_change_password?) && !send(:"current_#{scope}").impersonator_id
                return render json: { errors: { base: 'Password expired' } }, status: :conflict
              end
            end
          end
        end
      end
    end
  end
end
