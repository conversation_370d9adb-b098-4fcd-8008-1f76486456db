module Api
  module V1
    module B2B
      class CostInvoicesController < ApiController
        include Concerns::CostInvoiceable

        before_action :find_cost_invoice, except: %i[new create index perform_daily_worker]
        before_action :authorize
        before_action :set_form_data, only: %i[new show]

        def show
          @cost_invoice = @cost_invoice.decorate
          @cost_projects = @cost_invoice.cost_projects.includes(:accounting_number)
        end

        def index
          results = search(scope: scope, filters: params[:f]).results
          @cost_invoices = paginate(results, search_options(params))
          @cost_invoices = @cost_invoices.includes(:cost_invoice_positions, contractor: :user)
        end

        def accept
          ::B2B::AcceptCostInvoiceInteractor.call(cost_invoice: @cost_invoice, user: current_user,
                                                  force_accept: params[:force_accept])

          head :no_content
        rescue AASM::InvalidTransition => e
          payload = {
            issuer_time_report: @cost_invoice.issuer_time_report.to_h,
            not_allowed_accounting_numbers: @cost_invoice.not_allowed_accounting_numbers
                                                         .as_json(only: %i[id number description]),
            hours_worked: @cost_invoice.errors[:hours_worked].first
          }
          render_aasm_error(e, payload.compact)
        end

        def reject
          ::B2B::RejectCostInvoiceInteractor.call(cost_invoice: @cost_invoice, user: current_user)

          head :no_content
        end

        def withdraw
          ::B2B::WithdrawCostInvoiceInteractor.call(cost_invoice: @cost_invoice, user: current_user)

          head :no_content
        end

        private

        def global_create?
          policy(::B2B::CostInvoice).global_create?
        end

        def find_pending_cost_invoice
          @cost_invoice = scope.pending.or(scope.pending_department).find(params[:id])
        end

        def set_form_data
          @tax_rates = CostInvoicePosition.tax_rates.keys
          @global_create = global_create?
        end

        def cost_invoice_params
          params.require(:cost_invoice)
                .permit(policy(@cost_invoice || ::B2B::CostInvoice).permitted_attributes)
        end
      end
    end
  end
end
