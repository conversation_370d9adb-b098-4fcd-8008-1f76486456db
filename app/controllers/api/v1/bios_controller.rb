module Api
  module V1
    class BiosController < ApiController
      skip_after_action :verify_policy_scoped

      respond_to :json, :multipart_form

      def index
        authorize(Bio, :index?)
        set_departments_ids
        results = search(scope: users_scope.includes(:global_roles, :groups, :bio, :position), filters: params[:f]).results
        @users = paginate(authorize!(results))
        raise ActiveRecord::RecordNotFound, 'Page not found' if empty_page?
      end

      def create
        authorize(Bio, :create?)
        bio = Bio.new(bios_params)
        if bio.save!
          render json: bio, status: :created
        else
          render json: { errors: bio.errors.messages }, status: :unprocessable_entity
        end
      end

      def update
        authorize(Bio, :create?)
        bio = Bio.find(params[:id])
        if bio.update(bios_params)
          render json: bio
        else
          render json: bio.errors.messages, status: :unprocessable_entity
        end
      end

      def show
        authorize(Bio, :show?)
        @user = users_scope.find(params[:id])
        @bio = @user.bio
      end

      private

      def bios_params
        params.require(:bio).permit(
          :desc_en, :desc_pl, :publication_agreement, :agreement_notes, :user_id, :image, links_attributes: :name
        )
      end

      def search(options = {})
        @search = ::Searches::UserSearch.new(search_options(options))
      end

      def users_scope
        BioPolicy::Scope.new(current_user).resolve
      end

      def empty_page?
        params[:page].to_i > 1 && @users.empty?
      end

      # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      def set_departments_ids
        return unless params[:f] && params[:f][:departments_ids]

        ids = params[:f][:departments_ids]
        ids.map! do |id|
          case id
          when 'my_subordinates'
            Department.where('chief_id = :id or substitute_chief_id = :id',
                             id: current_user.id).pluck(:id)
          when 'my_teams'
            current_user.departments.pluck(:id)
          else
            id
          end
        end.flatten.uniq
        params[:f][:departments_ids] = ids.flatten.uniq
      end
      # rubocop:enable Metrics/AbcSize, Metrics/MethodLength
    end
  end
end
