class Api::V1::TrainingRequestsController < Api::V1::ApiController
  include Api::V1::Concerns::Managable

  before_action :find_training_request, except: %i[index create new]
  before_action :authorize

  def index
    search_object = search(scope: scope, filters: params[:f])
    results = search_object.results.includes(
      user: %i[department departments_as_uber_chief departments_as_supervisor]
    ).order(updated_at: :desc)
    @training_requests = paginate(results, search_options(params)).decorate
    @training_stats = training_stats(search_object)
  end

  def show
    @training_request = @training_request.decorate
  end

  def new
    @global_create = global_create?
  end

  def create
    result = TrainingRequests::CreateInteractor.call(training_request_params: training_request_params,
                                                     user: current_user,
                                                     global_create: global_create?)

    respond_with result.training_request
  end

  def accept
    result = TrainingRequests::AcceptInteractor.call(object: @training_request, force: params[:force],
                                                     user: current_user)

    manage_save(result)
  end

  def reject
    result = TrainingRequests::RejectInteractor.call(object: @training_request, user: current_user)

    manage_save(result)
  end

  def update
    @training_request.update(training_request_params.except(:user_id))

    respond_with @training_request
  end

  def destroy
    @training_request.destroy

    head :no_content
  end

  private

  def find_department(search_object)
    ids = search_object.searchable_department_ids(params.dig(:f, :departments_ids)).uniq.compact
    return unless ids.count == 1

    Department.find_by(id: ids)
  end

  def training_request_params
    params.require(:training_request).permit(:starts_on, :ends_on, :kind, :provider, :user_id, :place, :mode,
                                             :description, :tickets_price, :transportation_price, :accommodation_price)
  end

  def find_training_request
    @training_request = scope.find(params[:id])
  end

  def global_create?
    policy(TrainingRequest).global_create?
  end

  def scope
    policy_scope(TrainingRequest)
  end

  def search(options = {})
    ::Searches::TrainingRequestSearch.new(search_options(options), current_user)
  end

  def authorize
    super(@training_request || TrainingRequest)
  end

  def training_stats(search_object)
    department = find_department(search_object)
    return unless department && policy(department).show_training_stats?

    year = (params[:f][:date_from]&.split('-')&.first || Date.current.year).to_i
    yearly_limit = department.training_budget(year)
    accepted = accepted_this_year(department, year)
    {
      yearly_limit:, accepted_this_year: accepted, balance: yearly_limit - accepted
    }
  end

  def accepted_this_year(department, year)
    department.training_requests.where(starts_on: Date.new(year)..Date.new(year).end_of_year).accepted
              .sum('training_requests.tickets_price + training_requests.transportation_price +' \
                   'training_requests.accommodation_price')
  end
end
