module Api
  module V1
    class ActivitiesController < ApiController
      include ::Api::V1::Concerns::Documentation::ActivitiesEndpoint

      skip_after_action :verify_policy_scoped, only: [:index]

      def index
        authorize(Activity, :index?)
        results = search(scope: Activity.all, filters: params[:f]).results
        @activities = results.select { |i| Pundit.policy_scope!(current_user, Activity).include?(i) }
        respond_with(@activities)
      end

      private

      def search(options = {})
        @search = ::Searches::ActivitySearch.new(search_options(options))
      end
    end
  end
end
