module Api
  module V1
    class RolesController < ApiController
      include ::Api::V1::Concerns::Documentation::RolesEndpoint

      def index
        authorize(Role, :index?)
        results = search(scope: roles_scope, filters: params[:f]).results
        request.variant = :collection_for_select if params[:f].try(:[], :collection_for_select).to_s == 'true'
        respond_to do |format|
          format.json do |variant|
            variant.collection_for_select do
              @roles = authorize!(results)
              render template: '/api/v1/roles/index', collection: @roles
            end
            variant.none do
              @roles = paginate(authorize!(results), search_options(params))
              raise ActiveRecord::RecordNotFound, 'Page not found' if params[:page].to_i > 1 && @roles.empty?
            end
          end
        end
      end

      def show
        @role = authorize!(find_role)
        respond_with(@role.decorate)
      end

      def create
        authorize!(Role)
        role = Role.new(role_params)
        role.save
        respond_with(role.decorate)
      end

      def update
        @role = authorize!(find_role)
        @role.update(role_params)
        respond_with(@role.decorate)
      end

      def destroy
        @role = authorize!(find_role)
        @role.destroy
        respond_with(@role.decorate)
      end

      private

      def search(options = {})
        @search = ::Searches::RoleSearch.new(search_options(options))
      end

      def roles_scope
        Role.all
      end

      def find_role
        roles_scope.find(params[:id])
      end

      def find_roles
        roles_scope.find(params[:ids])
      end

      def role_params
        params.require(:role).permit(policy(Role).permitted_attributes)
      end
    end
  end
end
