module Api
  module V1
    module Concerns
      module Documentation
        module PublicKeysEndpoint
          include Swagger::Blocks
          extend ActiveSupport::Concern

          included do
            swagger_path '/api/public_keys' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              operation :get do
                key :description, "Provides a list of user's public keys"
                key :operationId, 'getPublicKeys'
                key :tags, ['public_keys']
                response 200 do
                  key :description, 'public_keys list response'
                  schema do
                    key :type, :array
                    items do
                      key :'$ref', :PublicKeyResponses
                    end
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                response 403 do
                  key :description, 'Forbidden'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
              operation :post do
                key :description, 'Adds a public key to user'
                key :operationId, 'createPublicKeys'
                key :tags, ['public_keys']
                key :produces, ['application/vnd.api+json; version=1; charset=utf-8']
                key :consumes, ['application/vnd.api+json; version=1; charset=utf-8']
                parameter do
                  key :name, :payload
                  key :in, :body
                  key :description, <<-HEREDOC.gsub(/(?:\n\r?|\r\n?)/, '<br>').squish.html_safe
                                      JSON form data for PublicKey to add
                                      Editable by ALL:
                                      key, identifier
                                    HEREDOC
                  key :required, true
                  schema do
                    key :'$ref', :PublicKeyRequest
                  end
                end
                response 201 do
                  key :description, 'Response with public key'
                  schema do
                    key :'$ref', :PublicKeyResponse
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                response 422 do
                  key :description, 'Unprocessable entity'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
            swagger_path '/api/public_keys/{id}' do
              parameter do
                key :name, 'Authorization'
                key :description, 'Your current Authorization Token (e.g. on staging server)'
                key :in, :header
                key :type, :string
              end
              parameter do
                key :name, 'X-Swagger-Sign-In-As'
                key :description, 'Sign in as user with ID (development env only)'
                key :in, :header
                key :required, true
                key :type, :string
              end
              parameter do
                key :name, :id
                key :in, :path
                key :description, 'ID of public key'
                key :required, true
                key :type, :integer
                key :format, :int64
              end
              operation :delete do
                key :description, 'deletes a single public key based on the ID supplied'
                key :operationId, 'deletePublicKey'
                key :tags, [
                  'public_keys'
                ]
                response 200 do
                  key :description, 'public key deleted'
                  schema do
                    key :'$ref', :PublicKeyResponse
                  end
                end
                response 401 do
                  key :description, 'Not Authorized'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                response 404 do
                  key :description, 'Record not found'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
                response 422 do
                  key :description, 'Unprocessable entity'
                  schema do
                    key :'$ref', :ApplicationErrorModel
                  end
                end
              end
            end
          end
        end
      end
    end
  end
end
