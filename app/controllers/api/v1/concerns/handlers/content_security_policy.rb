module Api
  module V1
    module Concerns
      module Handlers
        module ContentSecurityPolicy
          extend ActiveSupport::Concern

          included do
            before_action :set_csp

            protected

            def set_csp
              # w development przekopiowane do imperator-front/gulp/server.js
              # FIXME: na produkcji podać adminom
              csp = <<-HEREDOC.squish
                default-src 'self' https:; \
                font-src 'self' https: data:; \
                img-src 'self' https: data:; \
                object-src 'none'; \
                script-src 'self' https:; \
                style-src 'self' https: 'unsafe-inline'
              HEREDOC
              if Rails.env.production?
                headers['Content-Security-Policy'] = csp
              else
                headers['Content-Security-Policy-Report-Only'] = csp
              end
            end
          end
        end
      end
    end
  end
end
