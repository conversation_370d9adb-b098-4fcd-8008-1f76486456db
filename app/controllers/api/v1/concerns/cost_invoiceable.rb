module Api
  module V1
    module Concerns
      module CostInvoiceable
        def new; end

        def create
          result = "::#{parent_module}::CreateCostInvoiceInteractor".constantize.call(
            current_user: current_user,
            cost_invoice_params: cost_invoice_params,
            send_to_controller: params[:cost_invoice][:send_to_controller]
          )
          respond_with result.cost_invoice
        end

        def update # rubocop:disable Metrics/AbcSize
          result = "::#{parent_module}::UpdateCostInvoiceInteractor".constantize.call(
            cost_invoice: @cost_invoice,
            current_user: current_user,
            cost_invoice_params: cost_invoice_params,
            send_to_controller: params[:cost_invoice][:send_to_controller],
            comment: params[:cost_invoice][:comment]
          )
          result.cost_invoice.valid?(:prevent_indexing_errors) if result.cost_invoice.errors.any? # Workaround for https://github.com/rails/rails/issues/24390
          respond_with result.cost_invoice
        end

        def document
          document = @cost_invoice.document

          return head(:not_found) unless document

          file_path = Rails.root.join(document.storage.directory + document.id)
          send_file file_path, filename: document.original_filename,
                               type: document.mime_type,
                               disposition: 'inline'
        end

        def recall
          @cost_invoice.recall!(true) # Recall if the user is permitted anyway

          head :no_content
        end

        def history
          @snapshots = @cost_invoice.snapshots.includes(:snapshot_items, :user)
        end

        def perform_daily_worker
          args = [CostInvoicesDailyWorker::TYPES.invert[parent_module], Date.current.to_s]
          unless Sidekiq::ScheduledSet.new.any? { |job| job.klass == 'CostInvoicesDailyWorker' && job.args == args }
            CostInvoicesDailyWorker.perform_in(10.seconds, *args)
          end

          head :no_content
        end

        private

        def authorize
          super(@cost_invoice || "::#{parent_module}::CostInvoice".constantize)
        end

        def find_cost_invoice
          @cost_invoice = scope.find(params[:id])
        end

        def parent_module
          self.class.module_parent.name.demodulize
        end

        def scope
          policy_scope("::#{parent_module}::CostInvoice".constantize.order(invoice_date: :desc))
        end

        def search(options = {})
          "::Searches::#{parent_module}::CostInvoiceSearch".constantize.new(search_options(options),
                                                                            current_user)
        end
      end
    end
  end
end
