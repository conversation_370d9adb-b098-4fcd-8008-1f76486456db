require 'owncloud_provider'

class AcceptanceReportDocscloudWorker
  include Sidekiq::Worker

  def perform(attachment_id)
    attachment = Attachment.find(attachment_id)
    invoice = attachment.attachable
    project = invoice.project
    return unless provider.config_valid? && project.docs_cloud?

    file = attachment.file
    path = File.join(project.identifier, '05. Faktury i załączniki do faktur', invoice.id.to_s)
    provider.send_file(file, path, file.metadata['filename'])
  end

  private

  def provider
    @provider ||= OwncloudProvider.new('docscloud')
  end
end
