class RedmineTimeEntriesWorker
  include RedmineModelWorker

  def perform(action, user_id, holiday_request_id, *args)
    @user = User.find(user_id)
    return unless redmine_user?

    @holiday_request = HolidayRequest.find_by(id: holiday_request_id)
    range_args = args.each_slice(2).map { |first, last| Date.parse(first)..Date.parse(last) }
    send("perform_#{action}", *range_args)
  end

  private

  attr_reader :user, :holiday_request

  def perform_create(range)
    entry_ids = reported_in(range)
    delete_entries(entry_ids)
    create_entries(range)
  end

  def perform_update(old_range, new_range)
    old_entry_ids = reported_in(old_range)
    delete_entries(old_entry_ids)
    create_entries(new_range)
  end

  def perform_destroy(range)
    entry_ids = reported_in(range)
    delete_entries(entry_ids)
  end

  def create_entries(range)
    spent_on = range.begin
    spent_to = range.end
    RedmineApi.post('/time_entries.json',
                    body: { time_entry: {
                      user_id: user.redmine_id,
                      project_id: user.company.holiday_project_id,
                      issue_id: issue_id,
                      spent_on: spent_on, spent_to: spent_to, comments: '.',
                      hours: hours, include_non_working_days: include_non_working_days,
                      use_calendar_month_hours: use_calendar_month_hours,
                      activity_id: activity_id
                    } })
  end

  def issue_id
    user.company.redmine_holiday_issues_ids[holiday_request.category] || user.company.holiday_issue_id
  end

  def hours
    @holiday_request.hours
  end

  def include_non_working_days
    @holiday_request.non_working_day_request?
  end

  def use_calendar_month_hours
    hours.blank?
  end

  def activity_id
    Settings.redmine_absence_activity_id
  end

  def delete_entries(ids)
    return if ids.empty?

    r = RedmineApi.delete('/time_entries/destroy.json', body: { ids: ids })
    logger.debug 'destroy entries response:'
    logger.debug r.inspect
    r
  end

  def reported_in(range)
    from = range.begin
    to = range.end
    response = get_time_entries(from, to)
    handle_time_entries_response(response, from, to)
  end

  def handle_time_entries_response(response, from, to)
    time_entries = response['time_entries']
    ids = time_entries.map { |time_entry| time_entry['id'] }
    total_count = response['total_count']
    if time_entries.count < total_count
      (2..((total_count / time_entries.count) + 1)).each do |page|
        ids += get_time_entries(from, to, page)['time_entries'].map do |time_entry|
          time_entry['id']
        end
      end
    end
    ids
  end

  def get_time_entries(from, to, page = 1)
    RedmineApi.get('/time_entries.json',
                   query: { f: %w[user_id spent_on project_id],
                            op: { user_id: '=', spent_on: '><', project_id: '=' },
                            v: { user_id: [user.redmine_id.to_s],
                                 project_id: holiday_project_ids,
                                 spent_on: [from, to] },
                            limit: 100, page: page })
  end

  def holiday_project_ids
    @holiday_project_ids ||= Company.native.order(id: :asc).pluck(:holiday_project_id)
  end

  def redmine_user?
    user.redmine? && user.redmine_id
  end
end
