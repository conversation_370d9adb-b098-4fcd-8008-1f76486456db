class AutoChiefMembershipsWorker
  include Sidekiq::Worker

  def perform # rubocop:disable Metrics/AbcSize, Metrics/MethodLength, Metrics/CyclomaticComplexity
    chief_role_name = Settings.default_chief_role
    role_id = Role.find_or_create_by!(name: chief_role_name)&.id if chief_role_name

    return if role_id.blank?

    Project.active.find_each(batch_size: 100) do |project|
      memberships = project.memberships
      existing_chiefs_observer_ids = memberships
                                     .includes(:membership_roles)
                                     .where(membership_roles: { role_id: role_id, auto_created: true })
                                     .pluck(:member_id).uniq

      actual_chiefs_observer_ids = memberships
                                   .joins("JOIN users ON users.id = memberships.member_id AND memberships.member_type = 'User'")
                                   .joins('JOIN departments ON departments.id = users.department_id')
                                   .pluck('departments.chief_id, departments.uber_chief_id, departments.substitute_chief_id,
                                           departments.supervisor_id')
                                   .flatten
                                   .compact
                                   .uniq

      existing_user_ids = memberships.pluck(:member_id).uniq

      # unnecessary observers
      chiefs_observer_ids_to_remove = existing_chiefs_observer_ids - actual_chiefs_observer_ids
      # new observers
      chiefs_observer_ids_to_add = actual_chiefs_observer_ids - existing_user_ids

      if existing_chiefs_observer_ids.any? || actual_chiefs_observer_ids.any?
        ActiveRecord::Base.transaction do
          # remove unnecessary observers
          Membership.where(member_id: chiefs_observer_ids_to_remove).destroy_all if chiefs_observer_ids_to_remove.any?

          # adding new observers
          if chiefs_observer_ids_to_add.any?
            users = User.where(id: chiefs_observer_ids_to_add)

            users.find_each(batch_size: 100) do |user|
              project.add_user_to_project(user.id, role_id, auto_created: true)
            end
          end
        end
      end
    end
  end
end
