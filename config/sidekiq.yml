# Sample configuration file for Sidekiq.
# Options here can still be overridden by cmd line args.
#   RAILS_ENV=production bundle exec sidekiq -C config/sidekiq.yml
---
:verbose: false
:logfile: ./log/sidekiq.log # /home/<USER>/html/imperator/current/log/sidekiq.log
:pidfile: ./tmp/pids/sidekiq.pid # /home/<USER>/html/imperator/current/tmp/pids/sidekiq.pid
:concurrency: <%= ENV['DB_POOL'] || ENV['RAILS_MAX_THREADS'] || 5 %>
:queues:
  - owncloud
  - docscloud
  - imperator
  - default
  - ldap
  - redmine
  - click_up
  - active_job_<%= ENV['RAILS_ENV'] || 'development' %>_mailers
  - active_job_<%= ENV['RAILS_ENV'] || 'development' %>_default
  - active_job_<%= ENV['RAILS_ENV'] || 'development' %>_audit
development:
  :verbose: true
  # this config must be in 2 places, see config/initializers/sidekiq.rb
  :limits:
    ldap: 1
    redmine: 1
    owncloud: 1
    active_job_development_audit: 1
  # this config must be in 2 places, see config/initializers/sidekiq.rb
  :process_limits:
    ldap: 1
    redmine: 1
    owncloud: 1
    active_job_development_audit: 1
test:
  :verbose: false
  :concurrency: 1
production:
  :verbose: false
:scheduler:
  :schedule:
    absence_quota:
      cron: '0 7 * * *'
      class: AbsenceQuotaWorker
    active_employees_report_generator:
      cron: '0 0 * * 1'
      class: ActiveEmployeesReportGeneratorWorker
    attachments_cleanup:
      cron: '0 2 * * *'
      class: AttachmentsCleanupWorker
    cost_invoices_daily_hr:
      cron: '0 1 * * *'
      class: CostInvoicesDailyWorker
      args: ['B2B']
    cost_invoices_daily_dms:
      cron: '5 1 * * *'
      class: CostInvoicesDailyWorker
      args: ['DMS']
    cyclic_payments:
      cron: '0 2 * * *'
      class: CyclicPaymentsWorker
    dictionaries:
      cron: '0 3 * * *'
      class: DictionariesWorker
    evaluations:
      cron: '0 0 * * *'
      class: EvaluationsWorker
    holiday_request_convert_sick_to_vacation:
      cron: '0 0 * * *'
      class: HolidayRequestConvertSickToVacationWorker
    holiday_request_postponed_reminder:
      cron: '0 12 * * 1'
      class: HolidayRequestPostponedReminderWorker
    holiday_request_reminder:
      cron: '0 8 * * 1,2,3,4,5'
      class: HolidayRequestReminderWorker
    holiday_requests_report:
      at:
        - '2022-08-24'
        - '2022-09-23'
        - '2022-10-24'
        - '2022-11-23'
        - '2022-12-20'
      class: HolidayRequestsReportWorker
    invoice_allocation_version:
      cron: '0 20 * * *'
      class: InvoiceAllocationVersionsWorker
    invoice_attachment_chief_notification:
      cron: '0 20 * * 1'
      class: InvoiceAttachmentChiefNotificationWorker
    invoice_attachment_notification:
      cron: '0 20 * * 7'
      class: InvoiceAttachmentNotificationWorker
    password_expiration:
      cron: '0 7 * * *'
      class: PasswordExpirationWorker
    payment_schedules_reminder:
      cron: '0 * * * *'
      class: PaymentSchedulesReminderWorker
    reported_time:
      cron: '59 23 * * 1,4'
      class: ReportedTimeWorker
    reported_time_for_uber:
      cron: '0 23 * * 1,4'
      class: ReportedTimeForUberWorker
    rodo_reminder:
      cron: '0 6 * * 1'
      class: RodoReminderWorker
    assets_notification:
      cron: '0 7 * * *'
      class: AssetsNotificationWorker
    dms_cyclic_notifier:
      cron: '0 7 * * *'
      class: Dms::CyclicNotifierWorker
    project_responsible_reminder:
      cron: '0 18 5 * *'
      class: ProjectResponsibleReminderWorker
    invoice_documents:
      cron: '0 8 * * *'
      class: InvoiceDocumentsWorker
    pending_assets_reminder:
      cron: '0 10 * * 1'
      class: PendingAssetsReminderWorker
    docs_files_sync_worker:
      cron: '0 23 * * *'
      class: DocsFileSyncWorker
    block_users_worker:
      cron: '0 15 * * *'
      class: BlockUsersWorker
    user_entry_cards_worker:
      cron: '0 4 * * *'
      class: UserEntryCardsWorker
    resource_time_entries:
      cron: '0 5 * * *'
      class: ResourceTimeEntriesWorker
    remote_work_period_reminder:
      cron: '0 8 * * 1,2,3,4,5'
      class: RemoteWorkPeriodNotifierWorker
    remote_work_period_report:
      cron: '0 15 20-31 * 1,2,3,4,5'
      class: RemoteWorkPeriodReportWorker
    remove_old_files:
      cron: '0 3 * * *'
      class: RemoveOldFilesWorker
    validate_gus_contractors:
      cron: '0 4 * * 6' # Saturday at 4:00
      class: GusValidatorWorker
    idle_projects:
      cron: '0 13 1 * *'
      class: IdleProjectsWorker
    agreement_type_update:
      cron: '0 1 30 * *'
      class: AgreementTypeUpdateWorker
    user_contracts_reminder:
      cron: '0 10 * * *'
      class: UserContractsReminderWorker
    training_requests_reminder:
      cron: '0 8 * * 1,2,3,4,5'
      class: TrainingRequestNotifierWorker
    auto_chief_memberships_worker:
      cron: '30 2 * * *'
      class: AutoChiefMembershipsWorker
    invoices_processed_worker:
      cron: '0 21 * * 0'
      class: InvoicesProcessedWorker
    cost_invoices_currency:
      cron: '10 1 * * *'
      class: CostInvoicesCurrencyWorker
    click_up_report_worker:
      cron: '0 8 * * *'
      class: ClickUpReportWorker
    close_registry_activities:
      cron: '10 23 * * *'
      class: CloseRegistryActivitiesWorker
