# https://www.sitepoint.com/rails-model-caching-redis/
# For cache, use Rails.cache.fetch or fetch_multi instead (memcached) - so simple:
# @posts = Rails.cache.fetch('posts', expires_in: 5.minutes) do
# 	Post.all # fallback value
# end
if Settings.redis_enabled # use this only to use redis as database or a persisstent cache store
  require 'redis_connection'
  redis_options = Settings.redis.to_h
  redis_options[:size] = Integer(ENV['DB_POOL'] || ENV['RAILS_MAX_THREADS'] || 5)
  redis_options[:namespace] = [redis_options[:namespace].to_s, 'cache'].join('_')
  redis_options[:inherit_socket] = true
  ::REDIS = RedisConnection.new(redis_options).pool
end
